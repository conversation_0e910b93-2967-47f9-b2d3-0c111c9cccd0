# 🚀 Quick Start Guide - Cloud Engineering Capstone Deployer

## 🐍 Virtual Environment Setup (Recommended)

Since your Python environment is externally managed (Homebrew), we'll use a virtual environment for clean dependency management.

### Option 1: Automated Setup (Recommended)

```bash
# Run the setup script (creates virtual environment automatically)
python3 setup.py
```

This will:

- ✅ Create a virtual environment in `./venv/`
- ✅ Install all dependencies in isolation
- ✅ Create activation scripts for convenience
- ✅ Run installation tests

### Option 2: Manual Setup

```bash
# 1. Create virtual environment
python3 -m venv venv

# 2. Activate virtual environment
source venv/bin/activate  # macOS/Linux
# OR
venv\Scripts\activate     # Windows

# 3. Install dependencies
pip install -r requirements.txt

# 4. Test installation
python test_installation.py
```

## 🚀 Quick Deployment

### Step 1: Activate Environment

```bash
# Use the convenient activation script
source activate_capstone.sh

# OR manually activate
source venv/bin/activate
```

### Step 2: Configure AWS

```bash
aws configure
```

### Step 3: Deploy Infrastructure

```bash
# Run the main deployer
python deploy.py

# Select option 6 for full stack deployment
```

## 🎯 Menu Options

Once you run `python deploy.py`, you'll see:

```
============================================================
                    MAIN MENU
============================================================
  1. 🔍 Check Dependencies & Environment
  2. ⚙️  Configure AWS Credentials & Settings
  3. 🏗️  Deploy VPC Infrastructure
  4. 🚀 Deploy EKS Cluster
  5. 📦 Deploy Applications
  6. 🔄 Full Stack Deployment (VPC → EKS → Apps)
  7. 📊 Check Infrastructure Status
  8. 🔒 Security Scan & Compliance
  9. 📈 Monitoring & Health Check
  10. 💾 Backup & Disaster Recovery
  11. 🔧 CI/CD Pipeline Generation
  12. 🧹 Cleanup Resources
  13. 📋 View Logs
  14. ⚙️ Configuration Management
  0. ❌ Exit
============================================================
```

## 🌍 Environment-Specific Deployment

```bash
# Deploy to development (default)
python deploy.py --environment dev

# Deploy to staging
python deploy.py --environment staging

# Deploy to production
python deploy.py --environment prod
```

## 🔧 Advanced Usage

```bash
# Enable verbose logging
python deploy.py --verbose

# Use custom configuration
python deploy.py --config configs/custom.yaml

# Check dependencies only
python deploy.py --check-dependencies
```

## 🛠️ Troubleshooting

### Virtual Environment Issues

```bash
# If activation script doesn't work, manually activate:
source venv/bin/activate

# Verify you're in the virtual environment:
which python  # Should show ./venv/bin/python

# If dependencies are missing:
pip install -r requirements.txt
```

### AWS Issues

```bash
# Test AWS connectivity
aws sts get-caller-identity

# Configure AWS if needed
aws configure
```

### Permission Issues

```bash
# Make activation script executable
chmod +x activate_capstone.sh
```

## 📚 Documentation

- **README.md** - Comprehensive documentation
- **DEPLOYMENT_GUIDE.md** - Step-by-step deployment guide
- **FINAL_SUMMARY.md** - Complete project overview

## 💡 Tips

1. **Always activate the virtual environment first**
2. **Start with option 1 to check dependencies**
3. **Use option 6 for complete deployment**
4. **Check logs in `logs/` directory for troubleshooting**
5. **Use `deactivate` to exit the virtual environment**

## � Region Configuration

All environments are configured for **us-east-1** region:

- **Development**: us-east-1 (VPC: 10.0.0.0/16)
- **Staging**: us-east-1 (VPC: ********/16)
- **Production**: us-east-1 (VPC: ********/16)

Availability Zones: us-east-1a, us-east-1b, us-east-1c

## �🎉 Success!

Once setup is complete, you'll have a production-ready AWS infrastructure deployment system with:

- ✅ VPC with public/private subnets in us-east-1
- ✅ EKS cluster with managed node groups
- ✅ Application deployments
- ✅ Security scanning
- ✅ Monitoring and health checks
- ✅ Backup and recovery
- ✅ CI/CD pipeline generation

**Ready to deploy to us-east-1! 🚀**
