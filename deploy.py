#!/usr/bin/env python3
"""
Cloud Engineering Capstone Project - Main Deployment Orchestrator
Consolidates AWS VPC, EKS, and application deployment into a unified system.

Author: Cloud Engineering Cohort 9 - Group 1
Version: 1.0.0
"""

import os
import sys
import logging
import argparse
from typing import Optional, Dict, Any
from pathlib import Path

# Add utils directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from utils.dependency_checker import DependencyChecker
from utils.config_handler import ConfigHandler
from utils.aws_manager import AWSManager
from utils.terraform_manager import TerraformManager
from utils.k8s_manager import K8sManager
from utils.progress_tracker import ProgressTracker
from utils.monitoring_manager import MonitoringManager
from utils.backup_manager import BackupManager
from utils.security_manager import SecurityManager
from utils.cicd_manager import CICDManager


class CapstoneDeployer:
    """
    Main orchestrator class for the Cloud Engineering Capstone deployment system.
    Manages the complete lifecycle of AWS infrastructure deployment.
    """
    
    def __init__(self, config_path: Optional[str] = None, environment: str = "dev"):
        """
        Initialize the deployer with configuration and environment settings.
        
        Args:
            config_path: Path to configuration file
            environment: Target environment (dev/staging/prod)
        """
        self.environment = environment
        self.config_path = config_path or f"configs/{environment}.yaml"
        self.logger = self._setup_logging()
        
        # Initialize components
        self.dependency_checker = DependencyChecker()
        self.config_handler = ConfigHandler(self.config_path)
        self.progress_tracker = ProgressTracker()
        
        # Will be initialized after dependency check
        self.aws_manager: Optional[AWSManager] = None
        self.terraform_manager: Optional[TerraformManager] = None
        self.k8s_manager: Optional[K8sManager] = None
        self.monitoring_manager: Optional[MonitoringManager] = None
        self.backup_manager: Optional[BackupManager] = None
        self.security_manager: Optional[SecurityManager] = None
        self.cicd_manager: Optional[CICDManager] = None
        
        self.logger.info(f"Capstone Deployer initialized for environment: {environment}")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging system."""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(f"logs/capstone_{self.environment}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger(__name__)
    
    def display_banner(self):
        """Display the application banner."""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Cloud Engineering Capstone Project                        ║
║                         Cohort 9 - Group 1                                  ║
║                                                                              ║
║              AWS Infrastructure Deployment Automation                        ║
║                    VPC → EKS → Applications                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"Environment: {self.environment.upper()}")
        print(f"Configuration: {self.config_path}")
        print("=" * 80)
    
    def display_main_menu(self) -> str:
        """Display the main menu and get user selection."""
        menu_options = {
            "1": "🔍 Check Dependencies & Environment",
            "2": "⚙️  Configure AWS Credentials & Settings",
            "3": "🏗️  Deploy VPC Infrastructure",
            "4": "🚀 Deploy EKS Cluster",
            "5": "📦 Deploy Applications",
            "6": "🔄 Full Stack Deployment (VPC → EKS → Apps)",
            "7": "📊 Check Infrastructure Status",
            "8": "🔒 Security Scan & Compliance",
            "9": "📈 Monitoring & Health Check",
            "10": "💾 Backup & Disaster Recovery",
            "11": "🔧 CI/CD Pipeline Generation",
            "12": "🧹 Cleanup Resources",
            "13": "📋 View Logs",
            "14": "⚙️ Configuration Management",
            "0": "❌ Exit"
        }
        
        print("\n" + "=" * 60)
        print("                    MAIN MENU")
        print("=" * 60)
        
        for key, value in menu_options.items():
            print(f"  {key}. {value}")
        
        print("=" * 60)
        
        while True:
            try:
                choice = input("\nSelect an option (0-14): ").strip()
                if choice in menu_options:
                    return choice
                else:
                    print("❌ Invalid option. Please select a number between 0-14.")
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                sys.exit(0)
            except Exception as e:
                print(f"❌ Error reading input: {e}")
    
    def check_dependencies(self) -> bool:
        """Check and install required dependencies."""
        print("\n🔍 Checking Dependencies...")
        self.progress_tracker.start_task("Dependency Check")
        
        try:
            # Check all required tools
            dependencies_ok = self.dependency_checker.check_all_dependencies()
            
            if dependencies_ok:
                self.progress_tracker.complete_task("Dependency Check", "✅ All dependencies satisfied")
                print("✅ All dependencies are satisfied!")
                return True
            else:
                self.progress_tracker.fail_task("Dependency Check", "❌ Missing dependencies")
                print("❌ Some dependencies are missing. Please install them before proceeding.")
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking dependencies: {e}")
            self.progress_tracker.fail_task("Dependency Check", f"Error: {e}")
            return False
    
    def configure_aws(self) -> bool:
        """Configure AWS credentials and settings."""
        print("\n⚙️ Configuring AWS...")
        
        try:
            if not self.aws_manager:
                self.aws_manager = AWSManager(self.config_handler.get_aws_config())
            
            # Test AWS connectivity
            if self.aws_manager.test_connectivity():
                print("✅ AWS configuration successful!")
                return True
            else:
                print("❌ AWS configuration failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"Error configuring AWS: {e}")
            print(f"❌ Error configuring AWS: {e}")
            return False
    
    def deploy_vpc(self) -> bool:
        """Deploy VPC infrastructure."""
        print("\n🏗️ Deploying VPC Infrastructure...")
        self.progress_tracker.start_task("VPC Deployment")

        try:
            # Get configuration
            vpc_config = self.config_handler.get_vpc_config()
            aws_config = self.config_handler.get_aws_config()
            terraform_config = self.config_handler.get_terraform_config()

            # Prepare variables for VPC deployment
            variables = {
                "aws_region": aws_config.get("region", "us-west-2"),
                "project_name": "ce-capstone-grp1",
                "environment": self.environment,
                "vpc_name": f"ce-capstone-{self.environment}-vpc",
                "vpc_cidr": vpc_config.get("cidr_block", "10.0.0.0/16"),
                "availability_zones": vpc_config.get("availability_zones", ["us-west-2a", "us-west-2b", "us-west-2c"]),
                "private_subnets": vpc_config.get("private_subnets", ["*********/24", "*********/24", "*********/24"]),
                "public_subnets": vpc_config.get("public_subnets", ["********/24", "********/24", "********/24"]),
                "cluster_name": f"ce-capstone-{self.environment}",
                "enable_nat_gateway": vpc_config.get("enable_nat_gateway", True),
                "single_nat_gateway": True,
                "enable_dns_hostnames": vpc_config.get("enable_dns_hostnames", True),
                "enable_dns_support": vpc_config.get("enable_dns_support", True),
                "common_tags": aws_config.get("tags", {})
            }

            # Prepare backend configuration
            backend_config = {
                "bucket": terraform_config.get("backend", {}).get("bucket", f"ce-capstone-terraform-state-{self.environment}"),
                "key": "vpc/terraform.tfstate",
                "region": aws_config.get("region", "us-west-2"),
                "encrypt": True,
                "dynamodb_table": terraform_config.get("backend", {}).get("dynamodb_table", f"ce-capstone-terraform-locks-{self.environment}")
            }

            # Initialize Terraform manager
            if not self.terraform_manager:
                self.terraform_manager = TerraformManager(
                    working_dir="infrastructure/terraform/vpc",
                    config=terraform_config
                )

            # Deploy VPC
            success = self.terraform_manager.deploy("vpc", variables, backend_config)

            if success:
                self.progress_tracker.complete_task("VPC Deployment", "✅ VPC deployed successfully")
                print("✅ VPC infrastructure deployed successfully!")
                return True
            else:
                self.progress_tracker.fail_task("VPC Deployment", "❌ VPC deployment failed")
                print("❌ VPC deployment failed!")
                return False

        except Exception as e:
            self.logger.error(f"Error deploying VPC: {e}")
            self.progress_tracker.fail_task("VPC Deployment", f"Error: {e}")
            print(f"❌ Error deploying VPC: {e}")
            return False
    
    def deploy_eks(self) -> bool:
        """Deploy EKS cluster."""
        print("\n🚀 Deploying EKS Cluster...")
        self.progress_tracker.start_task("EKS Deployment")

        try:
            # Get configuration
            eks_config = self.config_handler.get_eks_config()
            aws_config = self.config_handler.get_aws_config()
            terraform_config = self.config_handler.get_terraform_config()

            # Prepare variables for EKS deployment
            variables = {
                "aws_region": aws_config.get("region", "us-west-2"),
                "project_name": "ce-capstone-grp1",
                "environment": self.environment,
                "cluster_name": eks_config.get("cluster_name", f"ce-capstone-{self.environment}"),
                "cluster_version": eks_config.get("cluster_version", "1.28"),
                "cluster_endpoint_private_access": True,
                "cluster_endpoint_public_access": True,
                "enable_cluster_creator_admin_permissions": True,
                "vpc_state_bucket": terraform_config.get("backend", {}).get("bucket", f"ce-capstone-terraform-state-{self.environment}"),
                "vpc_state_key": "vpc/terraform.tfstate",
                "common_tags": aws_config.get("tags", {}),
                "node_groups": eks_config.get("node_groups", {
                    "main": {
                        "instance_types": ["t3.medium"],
                        "desired_size": 2,
                        "min_size": 1,
                        "max_size": 3,
                        "disk_size": 20,
                        "ami_type": "AL2_x86_64"
                    }
                }),
                "access_entries": {}  # Can be configured per environment
            }

            # Prepare backend configuration
            backend_config = {
                "bucket": terraform_config.get("backend", {}).get("bucket", f"ce-capstone-terraform-state-{self.environment}"),
                "key": "eks/terraform.tfstate",
                "region": aws_config.get("region", "us-west-2"),
                "encrypt": True,
                "dynamodb_table": terraform_config.get("backend", {}).get("dynamodb_table", f"ce-capstone-terraform-locks-{self.environment}")
            }

            # Initialize Terraform manager for EKS
            eks_terraform_manager = TerraformManager(
                working_dir="infrastructure/terraform/eks",
                config=terraform_config
            )

            # Deploy EKS
            success = eks_terraform_manager.deploy("eks", variables, backend_config)

            if success:
                self.progress_tracker.complete_task("EKS Deployment", "✅ EKS deployed successfully")
                print("✅ EKS cluster deployed successfully!")
                return True
            else:
                self.progress_tracker.fail_task("EKS Deployment", "❌ EKS deployment failed")
                print("❌ EKS deployment failed!")
                return False

        except Exception as e:
            self.logger.error(f"Error deploying EKS: {e}")
            self.progress_tracker.fail_task("EKS Deployment", f"Error: {e}")
            print(f"❌ Error deploying EKS: {e}")
            return False
    
    def deploy_applications(self) -> bool:
        """Deploy applications to EKS."""
        print("\n📦 Deploying Applications...")
        self.progress_tracker.start_task("Application Deployment")
        
        try:
            if not self.k8s_manager:
                self.k8s_manager = K8sManager(self.config_handler.get_k8s_config())
            
            success = self.k8s_manager.deploy_applications()
            
            if success:
                self.progress_tracker.complete_task("Application Deployment", "✅ Applications deployed successfully")
                print("✅ Applications deployed successfully!")
                return True
            else:
                self.progress_tracker.fail_task("Application Deployment", "❌ Application deployment failed")
                print("❌ Application deployment failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"Error deploying applications: {e}")
            self.progress_tracker.fail_task("Application Deployment", f"Error: {e}")
            print(f"❌ Error deploying applications: {e}")
            return False
    
    def full_stack_deployment(self) -> bool:
        """Deploy the complete stack: VPC → EKS → Applications."""
        print("\n🔄 Starting Full Stack Deployment...")
        print("This will deploy: VPC → EKS → Applications")
        
        confirm = input("Are you sure you want to proceed? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ Full stack deployment cancelled.")
            return False
        
        # Deploy in sequence
        steps = [
            ("VPC Infrastructure", self.deploy_vpc),
            ("EKS Cluster", self.deploy_eks),
            ("Applications", self.deploy_applications)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📍 Deploying {step_name}...")
            if not step_func():
                print(f"❌ Full stack deployment failed at: {step_name}")
                return False
        
        print("\n🎉 Full stack deployment completed successfully!")
        return True
    
    def check_status(self):
        """Check the status of all infrastructure components."""
        print("\n📊 Checking Infrastructure Status...")
        
        try:
            if not self.aws_manager:
                self.aws_manager = AWSManager(self.config_handler.get_aws_config())
            
            status = self.aws_manager.get_infrastructure_status()
            
            print("\n" + "=" * 50)
            print("         INFRASTRUCTURE STATUS")
            print("=" * 50)
            
            for component, details in status.items():
                status_icon = "✅" if details.get("status") == "healthy" else "❌"
                print(f"{status_icon} {component}: {details.get('status', 'unknown')}")
                if details.get("details"):
                    print(f"   └─ {details['details']}")
            
        except Exception as e:
            self.logger.error(f"Error checking status: {e}")
            print(f"❌ Error checking status: {e}")
    
    def cleanup_resources(self):
        """Cleanup all deployed resources."""
        print("\n🧹 Resource Cleanup...")
        print("⚠️  WARNING: This will destroy ALL deployed resources!")
        
        confirm = input("Are you absolutely sure? Type 'DELETE' to confirm: ").strip()
        if confirm != 'DELETE':
            print("❌ Cleanup cancelled.")
            return
        
        try:
            # Cleanup in reverse order
            if self.k8s_manager:
                self.k8s_manager.cleanup()
            
            if self.terraform_manager:
                self.terraform_manager.destroy_all()
            
            print("✅ Cleanup completed!")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            print(f"❌ Error during cleanup: {e}")
    
    def view_logs(self):
        """Display recent logs."""
        print("\n📋 Recent Logs...")
        try:
            log_file = f"logs/capstone_{self.environment}.log"
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    # Show last 20 lines
                    for line in lines[-20:]:
                        print(line.strip())
            else:
                print("No log file found.")
        except Exception as e:
            print(f"❌ Error reading logs: {e}")
    
    def security_scan_menu(self):
        """Handle security scanning and compliance checks."""
        print("\n🔒 Security Scan & Compliance...")

        if not self.security_manager:
            self.security_manager = SecurityManager(self.config_handler.get_config())

        print("1. Run comprehensive security scan")
        print("2. Scan security groups")
        print("3. Scan IAM policies")
        print("4. Scan Kubernetes security")
        print("5. Generate security report")
        print("0. Back to main menu")

        choice = input("Select option: ").strip()

        if choice == "1":
            scan_results = self.security_manager.run_comprehensive_security_scan()
            self.security_manager.display_security_report(scan_results)
        elif choice == "2":
            results = self.security_manager.scan_security_groups()
            print(f"✅ Security group scan completed: {results.get('groups_with_issues', 0)} groups with issues")
        elif choice == "3":
            results = self.security_manager.scan_iam_policies()
            print(f"✅ IAM policy scan completed: {results.get('issues_found', 0)} issues found")
        elif choice == "4":
            cluster_name = self.config_handler.get_eks_config().get("cluster_name", f"ce-capstone-{self.environment}")
            results = self.security_manager.scan_kubernetes_security(cluster_name)
            print(f"✅ Kubernetes security scan completed: {results.get('issues_found', 0)} issues found")
        elif choice == "5":
            scan_results = self.security_manager.run_comprehensive_security_scan()
            recommendations = self.security_manager.generate_security_recommendations(scan_results)
            print("\n📋 Security Recommendations:")
            for rec in recommendations:
                print(f"  • {rec}")

    def monitoring_menu(self):
        """Handle monitoring and health checks."""
        print("\n📈 Monitoring & Health Check...")

        if not self.monitoring_manager:
            self.monitoring_manager = MonitoringManager(self.config_handler.get_config())

        print("1. Check infrastructure health")
        print("2. Setup CloudWatch dashboard")
        print("3. Create CloudWatch alarms")
        print("4. Setup log groups")
        print("5. View cost metrics")
        print("0. Back to main menu")

        choice = input("Select option: ").strip()

        if choice == "1":
            self.monitoring_manager.display_health_status()
        elif choice == "2":
            dashboard_name = f"capstone-{self.environment}"
            self.monitoring_manager.create_cloudwatch_dashboard(dashboard_name)
        elif choice == "3":
            self.monitoring_manager.create_cloudwatch_alarms()
        elif choice == "4":
            self.monitoring_manager.setup_log_groups()
        elif choice == "5":
            cost_metrics = self.monitoring_manager.get_cost_metrics()
            print(f"\n💰 Cost Metrics:")
            print(f"   Daily Cost: ${cost_metrics.get('daily_cost', 0):.2f}")
            print(f"   Monthly Estimate: ${cost_metrics.get('monthly_estimate', 0):.2f}")

    def backup_menu(self):
        """Handle backup and disaster recovery."""
        print("\n💾 Backup & Disaster Recovery...")

        if not self.backup_manager:
            self.backup_manager = BackupManager(self.config_handler.get_config())

        print("1. Create full backup")
        print("2. Backup Terraform state")
        print("3. Backup Kubernetes resources")
        print("4. Create EBS snapshots")
        print("5. List available backups")
        print("6. Restore from backup")
        print("0. Back to main menu")

        choice = input("Select option: ").strip()

        if choice == "1":
            self.backup_manager.full_backup()
        elif choice == "2":
            state_bucket = self.config_handler.get_terraform_config().get("backend", {}).get("bucket")
            backup_bucket = f"ce-capstone-backups-{self.environment}"
            if state_bucket:
                self.backup_manager.backup_terraform_state(state_bucket, backup_bucket)
            else:
                print("❌ No Terraform state bucket configured")
        elif choice == "3":
            cluster_name = self.config_handler.get_eks_config().get("cluster_name", f"ce-capstone-{self.environment}")
            backup_bucket = f"ce-capstone-backups-{self.environment}"
            self.backup_manager.backup_kubernetes_resources(cluster_name, backup_bucket)
        elif choice == "4":
            cluster_name = self.config_handler.get_eks_config().get("cluster_name", f"ce-capstone-{self.environment}")
            self.backup_manager.create_ebs_snapshots(cluster_name)
        elif choice == "5":
            backup_bucket = f"ce-capstone-backups-{self.environment}"
            backups = self.backup_manager.list_backups(backup_bucket)
            print(f"\n📋 Available Backups ({len(backups)}):")
            for i, backup in enumerate(backups[:10], 1):
                print(f"   {i}. {backup.get('backup_type', 'Unknown')} - {backup.get('timestamp', 'Unknown')}")
        elif choice == "6":
            print("⚠️ Restore functionality requires careful planning")
            print("Please contact your system administrator for restore procedures")

    def cicd_menu(self):
        """Handle CI/CD pipeline generation."""
        print("\n🔧 CI/CD Pipeline Generation...")

        if not self.cicd_manager:
            self.cicd_manager = CICDManager(self.config_handler.get_config())

        print("1. Generate GitHub Actions workflow")
        print("2. Generate GitLab CI pipeline")
        print("3. Generate Jenkins pipeline")
        print("4. Generate all pipelines")
        print("5. Create deployment scripts")
        print("0. Back to main menu")

        choice = input("Select option: ").strip()

        if choice == "1":
            self.cicd_manager.generate_github_actions_workflow()
        elif choice == "2":
            self.cicd_manager.generate_gitlab_ci_pipeline()
        elif choice == "3":
            self.cicd_manager.generate_jenkins_pipeline()
        elif choice == "4":
            pipelines = self.cicd_manager.generate_all_pipelines()
            print(f"✅ Generated {len(pipelines)} pipeline configurations")
        elif choice == "5":
            scripts = self.cicd_manager.create_deployment_scripts()
            print(f"✅ Created {len(scripts)} deployment scripts")

    def configuration_menu(self):
        """Handle configuration management."""
        print("\n⚙️ Configuration Management...")
        print("1. View current configuration")
        print("2. Edit configuration")
        print("3. Switch environment")
        print("0. Back to main menu")

        choice = input("Select option: ").strip()

        if choice == "1":
            self.config_handler.display_config()
        elif choice == "2":
            self.config_handler.edit_config()
        elif choice == "3":
            new_env = input("Enter new environment (dev/staging/prod): ").strip()
            if new_env in ["dev", "staging", "prod"]:
                self.environment = new_env
                self.config_path = f"configs/{new_env}.yaml"
                self.config_handler = ConfigHandler(self.config_path)
                print(f"✅ Switched to {new_env} environment")
            else:
                print("❌ Invalid environment")
    
    def run(self):
        """Main application loop."""
        self.display_banner()
        
        while True:
            try:
                choice = self.display_main_menu()
                
                if choice == "0":
                    print("\n👋 Thank you for using the Capstone Deployer!")
                    break
                elif choice == "1":
                    self.check_dependencies()
                elif choice == "2":
                    self.configure_aws()
                elif choice == "3":
                    self.deploy_vpc()
                elif choice == "4":
                    self.deploy_eks()
                elif choice == "5":
                    self.deploy_applications()
                elif choice == "6":
                    self.full_stack_deployment()
                elif choice == "7":
                    self.check_status()
                elif choice == "8":
                    self.security_scan_menu()
                elif choice == "9":
                    self.monitoring_menu()
                elif choice == "10":
                    self.backup_menu()
                elif choice == "11":
                    self.cicd_menu()
                elif choice == "12":
                    self.cleanup_resources()
                elif choice == "13":
                    self.view_logs()
                elif choice == "14":
                    self.configuration_menu()
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error: {e}")
                print(f"❌ Unexpected error: {e}")
                input("Press Enter to continue...")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Cloud Engineering Capstone Deployer")
    parser.add_argument("--environment", "-e", default="dev", 
                       choices=["dev", "staging", "prod"],
                       help="Target environment")
    parser.add_argument("--config", "-c", help="Path to configuration file")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        deployer = CapstoneDeployer(
            config_path=args.config,
            environment=args.environment
        )
        deployer.run()
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
