#!/usr/bin/env python3
"""
Cloud Engineering Capstone Project - Main Deployment Orchestrator
Consolidates AWS VPC, EKS, and application deployment into a unified system.

Author: Cloud Engineering Cohort 9 - Group 1
Version: 1.0.0
"""

import os
import sys
import logging
import argparse
from typing import Optional, Dict, Any
from pathlib import Path

# Add utils directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from utils.dependency_checker import DependencyChecker
from utils.config_handler import ConfigHandler
from utils.aws_manager import AWSManager
from utils.terraform_manager import TerraformManager
from utils.k8s_manager import K8sManager
from utils.progress_tracker import ProgressTracker


class CapstoneDeployer:
    """
    Main orchestrator class for the Cloud Engineering Capstone deployment system.
    Manages the complete lifecycle of AWS infrastructure deployment.
    """
    
    def __init__(self, config_path: Optional[str] = None, environment: str = "dev"):
        """
        Initialize the deployer with configuration and environment settings.
        
        Args:
            config_path: Path to configuration file
            environment: Target environment (dev/staging/prod)
        """
        self.environment = environment
        self.config_path = config_path or f"configs/{environment}.yaml"
        self.logger = self._setup_logging()
        
        # Initialize components
        self.dependency_checker = DependencyChecker()
        self.config_handler = ConfigHandler(self.config_path)
        self.progress_tracker = ProgressTracker()
        
        # Will be initialized after dependency check
        self.aws_manager: Optional[AWSManager] = None
        self.terraform_manager: Optional[TerraformManager] = None
        self.k8s_manager: Optional[K8sManager] = None
        
        self.logger.info(f"Capstone Deployer initialized for environment: {environment}")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging system."""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(f"logs/capstone_{self.environment}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger(__name__)
    
    def display_banner(self):
        """Display the application banner."""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Cloud Engineering Capstone Project                        ║
║                         Cohort 9 - Group 1                                  ║
║                                                                              ║
║              AWS Infrastructure Deployment Automation                        ║
║                    VPC → EKS → Applications                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"Environment: {self.environment.upper()}")
        print(f"Configuration: {self.config_path}")
        print("=" * 80)
    
    def display_main_menu(self) -> str:
        """Display the main menu and get user selection."""
        menu_options = {
            "1": "🔍 Check Dependencies & Environment",
            "2": "⚙️  Configure AWS Credentials & Settings",
            "3": "🏗️  Deploy VPC Infrastructure",
            "4": "🚀 Deploy EKS Cluster",
            "5": "📦 Deploy Applications",
            "6": "🔄 Full Stack Deployment (VPC → EKS → Apps)",
            "7": "📊 Check Infrastructure Status",
            "8": "🧹 Cleanup Resources",
            "9": "📋 View Logs",
            "10": "⚙️ Configuration Management",
            "0": "❌ Exit"
        }
        
        print("\n" + "=" * 60)
        print("                    MAIN MENU")
        print("=" * 60)
        
        for key, value in menu_options.items():
            print(f"  {key}. {value}")
        
        print("=" * 60)
        
        while True:
            try:
                choice = input("\nSelect an option (0-10): ").strip()
                if choice in menu_options:
                    return choice
                else:
                    print("❌ Invalid option. Please select a number between 0-10.")
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                sys.exit(0)
            except Exception as e:
                print(f"❌ Error reading input: {e}")
    
    def check_dependencies(self) -> bool:
        """Check and install required dependencies."""
        print("\n🔍 Checking Dependencies...")
        self.progress_tracker.start_task("Dependency Check")
        
        try:
            # Check all required tools
            dependencies_ok = self.dependency_checker.check_all_dependencies()
            
            if dependencies_ok:
                self.progress_tracker.complete_task("Dependency Check", "✅ All dependencies satisfied")
                print("✅ All dependencies are satisfied!")
                return True
            else:
                self.progress_tracker.fail_task("Dependency Check", "❌ Missing dependencies")
                print("❌ Some dependencies are missing. Please install them before proceeding.")
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking dependencies: {e}")
            self.progress_tracker.fail_task("Dependency Check", f"Error: {e}")
            return False
    
    def configure_aws(self) -> bool:
        """Configure AWS credentials and settings."""
        print("\n⚙️ Configuring AWS...")
        
        try:
            if not self.aws_manager:
                self.aws_manager = AWSManager(self.config_handler.get_aws_config())
            
            # Test AWS connectivity
            if self.aws_manager.test_connectivity():
                print("✅ AWS configuration successful!")
                return True
            else:
                print("❌ AWS configuration failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"Error configuring AWS: {e}")
            print(f"❌ Error configuring AWS: {e}")
            return False
    
    def deploy_vpc(self) -> bool:
        """Deploy VPC infrastructure."""
        print("\n🏗️ Deploying VPC Infrastructure...")
        self.progress_tracker.start_task("VPC Deployment")
        
        try:
            if not self.terraform_manager:
                self.terraform_manager = TerraformManager(
                    working_dir="infrastructure/terraform/vpc",
                    config=self.config_handler.get_terraform_config()
                )
            
            success = self.terraform_manager.deploy("vpc")
            
            if success:
                self.progress_tracker.complete_task("VPC Deployment", "✅ VPC deployed successfully")
                print("✅ VPC infrastructure deployed successfully!")
                return True
            else:
                self.progress_tracker.fail_task("VPC Deployment", "❌ VPC deployment failed")
                print("❌ VPC deployment failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"Error deploying VPC: {e}")
            self.progress_tracker.fail_task("VPC Deployment", f"Error: {e}")
            print(f"❌ Error deploying VPC: {e}")
            return False
    
    def deploy_eks(self) -> bool:
        """Deploy EKS cluster."""
        print("\n🚀 Deploying EKS Cluster...")
        self.progress_tracker.start_task("EKS Deployment")
        
        try:
            if not self.terraform_manager:
                self.terraform_manager = TerraformManager(
                    working_dir="infrastructure/terraform/eks",
                    config=self.config_handler.get_terraform_config()
                )
            
            success = self.terraform_manager.deploy("eks")
            
            if success:
                self.progress_tracker.complete_task("EKS Deployment", "✅ EKS deployed successfully")
                print("✅ EKS cluster deployed successfully!")
                return True
            else:
                self.progress_tracker.fail_task("EKS Deployment", "❌ EKS deployment failed")
                print("❌ EKS deployment failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"Error deploying EKS: {e}")
            self.progress_tracker.fail_task("EKS Deployment", f"Error: {e}")
            print(f"❌ Error deploying EKS: {e}")
            return False
    
    def deploy_applications(self) -> bool:
        """Deploy applications to EKS."""
        print("\n📦 Deploying Applications...")
        self.progress_tracker.start_task("Application Deployment")
        
        try:
            if not self.k8s_manager:
                self.k8s_manager = K8sManager(self.config_handler.get_k8s_config())
            
            success = self.k8s_manager.deploy_applications()
            
            if success:
                self.progress_tracker.complete_task("Application Deployment", "✅ Applications deployed successfully")
                print("✅ Applications deployed successfully!")
                return True
            else:
                self.progress_tracker.fail_task("Application Deployment", "❌ Application deployment failed")
                print("❌ Application deployment failed!")
                return False
                
        except Exception as e:
            self.logger.error(f"Error deploying applications: {e}")
            self.progress_tracker.fail_task("Application Deployment", f"Error: {e}")
            print(f"❌ Error deploying applications: {e}")
            return False
    
    def full_stack_deployment(self) -> bool:
        """Deploy the complete stack: VPC → EKS → Applications."""
        print("\n🔄 Starting Full Stack Deployment...")
        print("This will deploy: VPC → EKS → Applications")
        
        confirm = input("Are you sure you want to proceed? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ Full stack deployment cancelled.")
            return False
        
        # Deploy in sequence
        steps = [
            ("VPC Infrastructure", self.deploy_vpc),
            ("EKS Cluster", self.deploy_eks),
            ("Applications", self.deploy_applications)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📍 Deploying {step_name}...")
            if not step_func():
                print(f"❌ Full stack deployment failed at: {step_name}")
                return False
        
        print("\n🎉 Full stack deployment completed successfully!")
        return True
    
    def check_status(self):
        """Check the status of all infrastructure components."""
        print("\n📊 Checking Infrastructure Status...")
        
        try:
            if not self.aws_manager:
                self.aws_manager = AWSManager(self.config_handler.get_aws_config())
            
            status = self.aws_manager.get_infrastructure_status()
            
            print("\n" + "=" * 50)
            print("         INFRASTRUCTURE STATUS")
            print("=" * 50)
            
            for component, details in status.items():
                status_icon = "✅" if details.get("status") == "healthy" else "❌"
                print(f"{status_icon} {component}: {details.get('status', 'unknown')}")
                if details.get("details"):
                    print(f"   └─ {details['details']}")
            
        except Exception as e:
            self.logger.error(f"Error checking status: {e}")
            print(f"❌ Error checking status: {e}")
    
    def cleanup_resources(self):
        """Cleanup all deployed resources."""
        print("\n🧹 Resource Cleanup...")
        print("⚠️  WARNING: This will destroy ALL deployed resources!")
        
        confirm = input("Are you absolutely sure? Type 'DELETE' to confirm: ").strip()
        if confirm != 'DELETE':
            print("❌ Cleanup cancelled.")
            return
        
        try:
            # Cleanup in reverse order
            if self.k8s_manager:
                self.k8s_manager.cleanup()
            
            if self.terraform_manager:
                self.terraform_manager.destroy_all()
            
            print("✅ Cleanup completed!")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            print(f"❌ Error during cleanup: {e}")
    
    def view_logs(self):
        """Display recent logs."""
        print("\n📋 Recent Logs...")
        try:
            log_file = f"logs/capstone_{self.environment}.log"
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    # Show last 20 lines
                    for line in lines[-20:]:
                        print(line.strip())
            else:
                print("No log file found.")
        except Exception as e:
            print(f"❌ Error reading logs: {e}")
    
    def configuration_menu(self):
        """Handle configuration management."""
        print("\n⚙️ Configuration Management...")
        print("1. View current configuration")
        print("2. Edit configuration")
        print("3. Switch environment")
        print("0. Back to main menu")
        
        choice = input("Select option: ").strip()
        
        if choice == "1":
            self.config_handler.display_config()
        elif choice == "2":
            self.config_handler.edit_config()
        elif choice == "3":
            new_env = input("Enter new environment (dev/staging/prod): ").strip()
            if new_env in ["dev", "staging", "prod"]:
                self.environment = new_env
                self.config_path = f"configs/{new_env}.yaml"
                self.config_handler = ConfigHandler(self.config_path)
                print(f"✅ Switched to {new_env} environment")
            else:
                print("❌ Invalid environment")
    
    def run(self):
        """Main application loop."""
        self.display_banner()
        
        while True:
            try:
                choice = self.display_main_menu()
                
                if choice == "0":
                    print("\n👋 Thank you for using the Capstone Deployer!")
                    break
                elif choice == "1":
                    self.check_dependencies()
                elif choice == "2":
                    self.configure_aws()
                elif choice == "3":
                    self.deploy_vpc()
                elif choice == "4":
                    self.deploy_eks()
                elif choice == "5":
                    self.deploy_applications()
                elif choice == "6":
                    self.full_stack_deployment()
                elif choice == "7":
                    self.check_status()
                elif choice == "8":
                    self.cleanup_resources()
                elif choice == "9":
                    self.view_logs()
                elif choice == "10":
                    self.configuration_menu()
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error: {e}")
                print(f"❌ Unexpected error: {e}")
                input("Press Enter to continue...")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Cloud Engineering Capstone Deployer")
    parser.add_argument("--environment", "-e", default="dev", 
                       choices=["dev", "staging", "prod"],
                       help="Target environment")
    parser.add_argument("--config", "-c", help="Path to configuration file")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        deployer = CapstoneDeployer(
            config_path=args.config,
            environment=args.environment
        )
        deployer.run()
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
