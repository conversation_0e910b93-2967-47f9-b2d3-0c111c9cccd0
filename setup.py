#!/usr/bin/env python3
"""
Setup script for the Cloud Engineering Capstone Deployer.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """Print setup banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Cloud Engineering Capstone Setup                          ║
║                         Cohort 9 - Group 1                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   Minimum required: Python 3.8")
        return False

def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = [
        "logs",
        "infrastructure/terraform/vpc",
        "infrastructure/terraform/eks", 
        "infrastructure/terraform/modules",
        "infrastructure/kubernetes/applications",
        "infrastructure/kubernetes/monitoring"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   Created: {directory}")
    
    print("✅ Directories created successfully")
    return True

def run_installation_test():
    """Run the installation test."""
    print("\n🧪 Running installation test...")
    
    try:
        result = subprocess.run([sys.executable, "test_installation.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Installation test passed")
            return True
        else:
            print("❌ Installation test failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running installation test: {e}")
        return False

def show_next_steps():
    """Show next steps to the user."""
    print("\n" + "=" * 60)
    print("                    SETUP COMPLETE!")
    print("=" * 60)
    print("\n🎉 The Capstone Deployer has been set up successfully!")
    print("\n📋 Next Steps:")
    print("   1. Configure AWS credentials:")
    print("      aws configure")
    print("\n   2. Run the deployer:")
    print("      python deploy.py")
    print("\n   3. Select option 1 to check dependencies")
    print("   4. Follow the menu to deploy your infrastructure")
    print("\n💡 Tips:")
    print("   - Start with the development environment")
    print("   - Use option 6 for full stack deployment")
    print("   - Check logs in the logs/ directory if issues occur")
    print("\n📚 Documentation:")
    print("   - See README.md for detailed instructions")
    print("   - Check configs/ for environment settings")
    print("=" * 60)

def main():
    """Main setup function."""
    print_banner()
    
    steps = [
        ("Python Version Check", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Create Directories", create_directories),
        ("Installation Test", run_installation_test)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            print("Please resolve the issue and run setup again.")
            return False
    
    show_next_steps()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
