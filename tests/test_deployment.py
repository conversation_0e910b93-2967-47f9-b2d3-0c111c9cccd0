"""
Comprehensive test suite for the capstone deployment system.
"""

import pytest
import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add the parent directory to the path to import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.dependency_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.config_handler import ConfigHand<PERSON>
from utils.aws_manager import AWSManager
from utils.terraform_manager import TerraformManager
from utils.k8s_manager import K8sManager
from utils.progress_tracker import ProgressTracker
from utils.monitoring_manager import MonitoringManager
from utils.backup_manager import BackupManager
from utils.security_manager import SecurityManager
from utils.cicd_manager import CICDManager


class TestDependencyChecker:
    """Test the dependency checker functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.checker = DependencyChecker()
    
    def test_check_command_exists_python(self):
        """Test that Python command exists."""
        assert self.checker.check_command_exists("python3") == True
    
    def test_check_command_nonexistent(self):
        """Test checking for non-existent command."""
        assert self.checker.check_command_exists("nonexistent-command-12345") == False
    
    def test_version_comparison(self):
        """Test version comparison logic."""
        assert self.checker.compare_versions("3.9.0", "3.8.0") == True
        assert self.checker.compare_versions("3.8.0", "3.9.0") == False
        assert self.checker.compare_versions("3.8.0", "3.8.0") == True
    
    def test_get_installation_instructions(self):
        """Test getting installation instructions."""
        instructions = self.checker.get_installation_instructions("terraform")
        assert isinstance(instructions, str)
        assert len(instructions) > 0


class TestConfigHandler:
    """Test the configuration handler."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.yaml")
        self.handler = ConfigHandler(self.config_file)
    
    def teardown_method(self):
        """Cleanup test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_default_config_creation(self):
        """Test that default config is created."""
        assert os.path.exists(self.config_file)
        config = self.handler.get_config()
        assert "environment" in config
        assert "aws" in config
        assert "vpc" in config
    
    def test_get_aws_config(self):
        """Test getting AWS configuration."""
        aws_config = self.handler.get_aws_config()
        assert isinstance(aws_config, dict)
        assert "region" in aws_config
    
    def test_set_and_get_config(self):
        """Test setting and getting configuration values."""
        self.handler.set_config("test.value", "test_data")
        assert self.handler.get_config("test.value") == "test_data"
    
    def test_environment_variables(self):
        """Test environment variable generation."""
        env_vars = self.handler.get_environment_variables()
        assert isinstance(env_vars, dict)
        assert "AWS_DEFAULT_REGION" in env_vars


class TestProgressTracker:
    """Test the progress tracking functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.tracker = ProgressTracker()
    
    def test_task_lifecycle(self):
        """Test complete task lifecycle."""
        # Start task
        self.tracker.start_task("test_task", "Testing task")
        assert "test_task" in self.tracker.tasks
        assert self.tracker.tasks["test_task"].is_active
        
        # Update task
        self.tracker.update_task("test_task", progress=0.5, message="Half done")
        assert self.tracker.tasks["test_task"].progress == 0.5
        
        # Complete task
        self.tracker.complete_task("test_task", "Task completed")
        assert self.tracker.tasks["test_task"].is_completed
        assert self.tracker.tasks["test_task"].progress == 1.0
    
    def test_task_failure(self):
        """Test task failure handling."""
        self.tracker.start_task("fail_task", "Testing failure")
        self.tracker.fail_task("fail_task", "Task failed")
        assert self.tracker.tasks["fail_task"].status.value == "failed"
    
    def test_progress_bar_generation(self):
        """Test progress bar generation."""
        progress_bar = self.tracker.get_progress_bar(0.5, 20)
        assert "[" in progress_bar
        assert "]" in progress_bar
        assert "50%" in progress_bar
    
    def test_summary_generation(self):
        """Test summary generation."""
        self.tracker.start_task("task1", "Task 1")
        self.tracker.complete_task("task1", "Completed")
        self.tracker.start_task("task2", "Task 2")
        self.tracker.fail_task("task2", "Failed")
        
        summary = self.tracker.get_summary()
        assert summary["total_tasks"] == 2
        assert summary["completed"] == 1
        assert summary["failed"] == 1


class TestTerraformManager:
    """Test Terraform management functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {"workspace": "test"}
        self.manager = TerraformManager(self.temp_dir, self.config)
    
    def teardown_method(self):
        """Cleanup test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test manager initialization."""
        assert self.manager.working_dir == Path(self.temp_dir)
        assert self.manager.config == self.config
    
    def test_tfvars_file_creation(self):
        """Test terraform.tfvars file creation."""
        variables = {
            "region": "us-west-2",
            "instance_count": 2,
            "enable_monitoring": True,
            "subnets": ["subnet-1", "subnet-2"]
        }
        
        tfvars_file = self.manager.create_tfvars_file(variables)
        assert os.path.exists(tfvars_file)
        
        with open(tfvars_file, 'r') as f:
            content = f.read()
            assert 'region = "us-west-2"' in content
            assert 'instance_count = 2' in content
            assert 'enable_monitoring = true' in content
    
    def test_backend_config_creation(self):
        """Test backend configuration file creation."""
        backend_config = {
            "bucket": "test-bucket",
            "key": "test.tfstate",
            "region": "us-west-2",
            "encrypt": True
        }
        
        backend_file = self.manager.create_backend_config("test", backend_config)
        assert os.path.exists(backend_file)
        
        with open(backend_file, 'r') as f:
            content = f.read()
            assert 'bucket = "test-bucket"' in content
            assert 'encrypt = true' in content


class TestCICDManager:
    """Test CI/CD pipeline generation."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)
        self.config = {"environment": "test", "project_name": "test-project"}
        self.manager = CICDManager(self.config)
    
    def teardown_method(self):
        """Cleanup test environment."""
        os.chdir("/")
        shutil.rmtree(self.temp_dir)
    
    def test_github_actions_generation(self):
        """Test GitHub Actions workflow generation."""
        workflow_file = self.manager.generate_github_actions_workflow()
        assert os.path.exists(workflow_file)
        assert workflow_file.endswith(".github/workflows/deploy.yml")
        
        with open(workflow_file, 'r') as f:
            content = f.read()
            assert "name: Capstone Infrastructure Deployment" in content
            assert "terraform-plan" in content
    
    def test_gitlab_ci_generation(self):
        """Test GitLab CI pipeline generation."""
        pipeline_file = self.manager.generate_gitlab_ci_pipeline()
        assert os.path.exists(pipeline_file)
        assert pipeline_file.endswith(".gitlab-ci.yml")
        
        with open(pipeline_file, 'r') as f:
            content = f.read()
            assert "stages:" in content
            assert "validate" in content
    
    def test_jenkins_pipeline_generation(self):
        """Test Jenkins pipeline generation."""
        pipeline_file = self.manager.generate_jenkins_pipeline()
        assert os.path.exists(pipeline_file)
        assert pipeline_file.endswith("Jenkinsfile")
        
        with open(pipeline_file, 'r') as f:
            content = f.read()
            assert "pipeline {" in content
            assert "stages {" in content


class TestAWSManager:
    """Test AWS management functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.config = {
            "region": "us-west-2",
            "profile": "default",
            "tags": {"Project": "test"}
        }
    
    @patch('boto3.Session')
    def test_initialization_with_mock(self, mock_session):
        """Test AWS manager initialization with mocked boto3."""
        mock_session.return_value.client.return_value = Mock()
        manager = AWSManager(self.config)
        assert manager.region == "us-west-2"
        assert manager.profile == "default"
    
    @patch('boto3.Session')
    def test_connectivity_test_success(self, mock_session):
        """Test successful connectivity test."""
        mock_sts = Mock()
        mock_sts.get_caller_identity.return_value = {
            'Account': '************',
            'Arn': 'arn:aws:iam::************:user/test'
        }
        mock_session.return_value.client.return_value = mock_sts
        
        manager = AWSManager(self.config)
        manager.sts = mock_sts
        
        result = manager.test_connectivity()
        assert result == True
    
    @patch('boto3.Session')
    def test_connectivity_test_failure(self, mock_session):
        """Test failed connectivity test."""
        mock_sts = Mock()
        mock_sts.get_caller_identity.side_effect = Exception("Access denied")
        mock_session.return_value.client.return_value = mock_sts
        
        manager = AWSManager(self.config)
        manager.sts = mock_sts
        
        result = manager.test_connectivity()
        assert result == False


class TestIntegration:
    """Integration tests for the complete system."""
    
    def setup_method(self):
        """Setup integration test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.yaml")
    
    def teardown_method(self):
        """Cleanup integration test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_config_to_terraform_integration(self):
        """Test configuration to Terraform variable integration."""
        # Create config handler
        config_handler = ConfigHandler(self.config_file)
        
        # Get VPC config
        vpc_config = config_handler.get_vpc_config()
        assert isinstance(vpc_config, dict)
        
        # Test environment variable generation
        env_vars = config_handler.get_environment_variables()
        assert "AWS_DEFAULT_REGION" in env_vars
        assert "TF_VAR_environment" in env_vars
    
    def test_progress_tracking_integration(self):
        """Test progress tracking with multiple components."""
        tracker = ProgressTracker()
        
        # Simulate deployment workflow
        tracker.start_task("vpc_deployment", "Deploying VPC")
        tracker.update_task("vpc_deployment", 0.3, "Creating subnets")
        tracker.update_task("vpc_deployment", 0.7, "Creating gateways")
        tracker.complete_task("vpc_deployment", "VPC deployed successfully")
        
        tracker.start_task("eks_deployment", "Deploying EKS")
        tracker.update_task("eks_deployment", 0.5, "Creating cluster")
        tracker.complete_task("eks_deployment", "EKS deployed successfully")
        
        summary = tracker.get_summary()
        assert summary["total_tasks"] == 2
        assert summary["completed"] == 2
        assert summary["success_rate"] == 100.0


# Test fixtures and utilities
@pytest.fixture
def sample_config():
    """Provide a sample configuration for testing."""
    return {
        "environment": "test",
        "aws": {
            "region": "us-west-2",
            "profile": "default",
            "tags": {
                "Project": "test-project",
                "Environment": "test"
            }
        },
        "vpc": {
            "cidr_block": "10.0.0.0/16",
            "availability_zones": ["us-west-2a", "us-west-2b"],
            "public_subnets": ["10.0.1.0/24", "10.0.2.0/24"],
            "private_subnets": ["10.0.11.0/24", "10.0.12.0/24"]
        },
        "eks": {
            "cluster_name": "test-cluster",
            "cluster_version": "1.28"
        }
    }


@pytest.fixture
def temp_workspace():
    """Provide a temporary workspace for testing."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


# Performance tests
class TestPerformance:
    """Performance tests for critical components."""
    
    def test_config_loading_performance(self, temp_workspace):
        """Test configuration loading performance."""
        import time
        
        config_file = os.path.join(temp_workspace, "perf_config.yaml")
        
        start_time = time.time()
        config_handler = ConfigHandler(config_file)
        config = config_handler.get_config()
        end_time = time.time()
        
        # Configuration loading should be fast
        assert (end_time - start_time) < 1.0
        assert config is not None
    
    def test_progress_tracker_performance(self):
        """Test progress tracker performance with many tasks."""
        import time
        
        tracker = ProgressTracker()
        
        start_time = time.time()
        
        # Create many tasks
        for i in range(100):
            tracker.start_task(f"task_{i}", f"Task {i}")
            tracker.update_task(f"task_{i}", 0.5, "In progress")
            tracker.complete_task(f"task_{i}", "Completed")
        
        end_time = time.time()
        
        # Should handle 100 tasks quickly
        assert (end_time - start_time) < 2.0
        
        summary = tracker.get_summary()
        assert summary["total_tasks"] == 100
        assert summary["completed"] == 100


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
