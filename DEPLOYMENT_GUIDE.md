# 🚀 Cloud Engineering Capstone - Unified Deployment Guide

## 📋 Overview

This guide walks you through deploying the complete AWS infrastructure stack using the unified automation system that consolidates:

- **VPC Infrastructure** (from ce-grp-1-vpc)
- **EKS Cluster** (from ce-grp-1-eks)  
- **Applications** (from ce-grp-1-apps)

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Unified Deployment System                │
├─────────────────────────────────────────────────────────────┤
│  Python Orchestrator (deploy.py)                          │
│  ├── VPC Module (Terraform)                               │
│  ├── EKS Module (Terraform)                               │
│  └── Applications (Kubernetes)                            │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Prerequisites

### Required Tools
- **AWS CLI** v2.0+
- **Terraform** v1.0+
- **kubectl** v1.20+
- **Helm** v3.0+
- **Docker** v20.0+
- **Python** v3.8+

### AWS Setup
1. **Configure AWS credentials:**
   ```bash
   aws configure
   ```

2. **Create S3 bucket for Terraform state:**
   ```bash
   aws s3 mb s3://ce-capstone-terraform-state-dev --region us-west-2
   ```

3. **Create DynamoDB table for state locking:**
   ```bash
   aws dynamodb create-table \
     --table-name ce-capstone-terraform-locks-dev \
     --attribute-definitions AttributeName=LockID,AttributeType=S \
     --key-schema AttributeName=LockID,KeyType=HASH \
     --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
     --region us-west-2
   ```

## 🚀 Quick Start

### 1. Setup the Environment
```bash
# Clone and setup
git clone <your-repo-url>
cd ce-grp-1-automation

# Run setup script
python3 setup.py

# Or manual setup:
pip install -r requirements.txt
```

### 2. Run the Deployer
```bash
python3 deploy.py
```

### 3. Follow the Interactive Menu
```
============================================================
                    MAIN MENU
============================================================
  1. 🔍 Check Dependencies & Environment
  2. ⚙️  Configure AWS Credentials & Settings
  3. 🏗️  Deploy VPC Infrastructure
  4. 🚀 Deploy EKS Cluster
  5. 📦 Deploy Applications
  6. 🔄 Full Stack Deployment (VPC → EKS → Apps)
  7. 📊 Check Infrastructure Status
  8. 🧹 Cleanup Resources
  9. 📋 View Logs
  10. ⚙️ Configuration Management
  0. ❌ Exit
============================================================
```

## 📝 Step-by-Step Deployment

### Step 1: Check Dependencies
```bash
# Select option 1 from menu
1. 🔍 Check Dependencies & Environment
```
This will:
- ✅ Validate all required tools
- 🔧 Auto-install missing Python packages
- 💡 Provide installation instructions for missing tools

### Step 2: Configure AWS
```bash
# Select option 2 from menu
2. ⚙️ Configure AWS Credentials & Settings
```
This will:
- ✅ Test AWS connectivity
- 🔍 Verify credentials and permissions
- 📊 Display account information

### Step 3: Deploy Infrastructure

#### Option A: Full Stack Deployment (Recommended)
```bash
# Select option 6 from menu
6. 🔄 Full Stack Deployment (VPC → EKS → Apps)
```
This will deploy everything in sequence:
1. 🏗️ VPC Infrastructure
2. 🚀 EKS Cluster
3. 📦 Applications

#### Option B: Step-by-Step Deployment
```bash
# Deploy VPC first
3. 🏗️ Deploy VPC Infrastructure

# Then deploy EKS
4. 🚀 Deploy EKS Cluster

# Finally deploy applications
5. 📦 Deploy Applications
```

## 🔧 Configuration

### Environment Files
The system supports multiple environments:

- `configs/dev.yaml` - Development
- `configs/staging.yaml` - Staging  
- `configs/prod.yaml` - Production

### Key Configuration Sections

#### VPC Configuration
```yaml
vpc:
  cidr_block: 10.0.0.0/16
  availability_zones:
    - us-west-2a
    - us-west-2b
    - us-west-2c
  public_subnets:
    - ********/24
    - ********/24
    - ********/24
  private_subnets:
    - *********/24
    - *********/24
    - *********/24
```

#### EKS Configuration
```yaml
eks:
  cluster_name: ce-capstone-dev
  cluster_version: "1.28"
  node_groups:
    main:
      instance_types:
        - t3.medium
      min_size: 1
      max_size: 3
      desired_size: 2
```

## 🔍 Monitoring & Status

### Check Infrastructure Status
```bash
# Select option 7 from menu
7. 📊 Check Infrastructure Status
```

This provides real-time status of:
- ✅ VPC and networking components
- ✅ EKS cluster health
- ✅ Application deployments
- ✅ Terraform backend status

### View Logs
```bash
# Select option 9 from menu
9. 📋 View Logs
```

Logs are stored in:
- `logs/capstone_dev.log`
- `logs/capstone_staging.log`
- `logs/capstone_prod.log`

## 🧹 Cleanup

### Destroy All Resources
```bash
# Select option 8 from menu
8. 🧹 Cleanup Resources
```

⚠️ **WARNING**: This will destroy ALL deployed resources!

You'll need to type `DELETE` to confirm.

## 🔧 Advanced Usage

### Deploy to Specific Environment
```bash
python3 deploy.py --environment prod
```

### Use Custom Configuration
```bash
python3 deploy.py --config configs/custom.yaml
```

### Enable Verbose Logging
```bash
python3 deploy.py --verbose
```

## 📁 Project Structure

```
ce-grp-1-automation/
├── deploy.py                 # Main orchestrator
├── utils/                   # Core utilities
│   ├── dependency_checker.py # Tool validation
│   ├── terraform_manager.py # Terraform operations
│   ├── k8s_manager.py       # Kubernetes operations
│   └── aws_manager.py       # AWS operations
├── infrastructure/          # Infrastructure code
│   ├── terraform/           # Terraform modules
│   │   ├── vpc/            # VPC module (from ce-grp-1-vpc)
│   │   └── eks/            # EKS module (from ce-grp-1-eks)
│   └── kubernetes/         # K8s manifests
│       └── applications/   # Apps (from ce-grp-1-apps)
└── configs/                # Environment configs
    ├── dev.yaml
    ├── staging.yaml
    └── prod.yaml
```

## 🚨 Troubleshooting

### Common Issues

1. **AWS Credentials Not Found**
   ```bash
   aws configure
   ```

2. **Terraform Backend Issues**
   - Ensure S3 bucket exists
   - Check DynamoDB table for locks
   - Verify IAM permissions

3. **EKS Connection Issues**
   ```bash
   aws eks update-kubeconfig --region us-west-2 --name ce-capstone-dev
   ```

4. **Application Deployment Failures**
   ```bash
   kubectl logs -n capstone-dev <pod-name>
   ```

### Getting Help

1. Check logs in `logs/` directory
2. Use verbose mode: `python3 deploy.py --verbose`
3. Review infrastructure status from menu option 7
4. Check AWS CloudWatch for detailed metrics

## 🎯 Success Criteria

After successful deployment, you should have:

- ✅ VPC with public/private subnets across 3 AZs
- ✅ EKS cluster with managed node groups
- ✅ Applications running in Kubernetes
- ✅ LoadBalancer services accessible externally
- ✅ All resources properly tagged and monitored

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review application logs
3. Verify AWS resource status in console
4. Check Terraform state files in S3

---

**🎉 Congratulations!** You now have a unified, production-ready AWS infrastructure deployment system that consolidates all three original repositories into a single, intelligent automation platform.
