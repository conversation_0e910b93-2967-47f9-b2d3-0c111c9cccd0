#!/usr/bin/env python3
"""
Test script to verify the capstone deployer installation.
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing module imports...")

    try:
        # Add utils to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

        from utils.dependency_checker import DependencyChe<PERSON>
        from utils.config_handler import Config<PERSON>andler
        from utils.aws_manager import AWSManager
        from utils.terraform_manager import TerraformManager
        from utils.k8s_manager import K8sManager
        from utils.progress_tracker import ProgressTracker

        # Store classes globally for other tests
        globals()['DependencyChecker'] = DependencyChecker
        globals()['ConfigHandler'] = ConfigHandler
        globals()['AWSManager'] = AWSManager
        globals()['TerraformManager'] = TerraformManager
        globals()['K8sManager'] = K8sManager
        globals()['ProgressTracker'] = ProgressTracker

        print("✅ All modules imported successfully!")
        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_config_loading():
    """Test configuration loading."""
    print("\n🧪 Testing configuration loading...")

    try:
        if 'ConfigHandler' not in globals():
            print("❌ ConfigHandler not available - imports may have failed")
            return False

        config_handler = globals()['ConfigHandler']("configs/dev.yaml")
        config = config_handler.get_config()

        if config and "environment" in config:
            print(f"✅ Configuration loaded successfully for environment: {config['environment']}")
            return True
        else:
            print("❌ Configuration is empty or invalid")
            return False

    except Exception as e:
        print(f"❌ Configuration loading error: {e}")
        return False

def test_dependency_checker():
    """Test dependency checker."""
    print("\n🧪 Testing dependency checker...")

    try:
        if 'DependencyChecker' not in globals():
            print("❌ DependencyChecker not available - imports may have failed")
            return False

        checker = globals()['DependencyChecker']()

        # Test a simple tool check
        exists = checker.check_command_exists("python3")
        if exists:
            print("✅ Dependency checker working correctly")
            return True
        else:
            print("❌ Dependency checker failed basic test")
            return False

    except Exception as e:
        print(f"❌ Dependency checker error: {e}")
        return False

def test_progress_tracker():
    """Test progress tracker."""
    print("\n🧪 Testing progress tracker...")

    try:
        if 'ProgressTracker' not in globals():
            print("❌ ProgressTracker not available - imports may have failed")
            return False

        tracker = globals()['ProgressTracker']()

        # Test basic operations
        tracker.start_task("test_task", "Testing progress tracker")
        tracker.update_task("test_task", progress=0.5, message="Half way done")
        tracker.complete_task("test_task", "Test completed")

        summary = tracker.get_summary()
        if summary["total_tasks"] == 1 and summary["completed"] == 1:
            print("✅ Progress tracker working correctly")
            return True
        else:
            print("❌ Progress tracker failed basic test")
            return False

    except Exception as e:
        print(f"❌ Progress tracker error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Capstone Deployer Installation Test")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration Loading", test_config_loading),
        ("Dependency Checker", test_dependency_checker),
        ("Progress Tracker", test_progress_tracker)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Installation is working correctly.")
        print("\nYou can now run the main deployer:")
        print("  python deploy.py")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
