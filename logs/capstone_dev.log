2025-05-30 03:40:47,410 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 03:40:47,410 - __main__ - INFO - <PERSON>stone Deployer initialized for environment: dev
2025-05-30 04:05:44,575 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:05:44,575 - __main__ - INFO - <PERSON>stone Deployer initialized for environment: dev
2025-05-30 04:13:26,553 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:13:26,553 - __main__ - INFO - <PERSON><PERSON> Deployer initialized for environment: dev
2025-05-30 04:15:02,952 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:15:02,952 - __main__ - INFO - <PERSON>stone Deployer initialized for environment: dev
2025-05-30 04:17:44,734 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:17:46,329 - utils.aws_manager - INFO - AWS connection successful - Account: ************, User: arn:aws:iam::************:user/azni_ce9
2025-05-30 04:22:29,924 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:22:29,924 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:23:30,269 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:23:30,269 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:23:40,574 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:23:42,062 - utils.aws_manager - INFO - AWS connection successful - Account: ************, User: arn:aws:iam::************:user/azni_ce9
2025-05-30 04:23:50,295 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config infrastructure/terraform/vpc/backend.hcl
2025-05-30 04:23:52,201 - utils.terraform_manager - ERROR - Command failed: terraform init -upgrade -backend-config infrastructure/terraform/vpc/backend.hcl
2025-05-30 04:23:52,201 - utils.terraform_manager - ERROR - Error: [31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mInvalid backend configuration value[0m
[31m│[0m [0m
[31m│[0m [0m[0mInvalid backend configuration argument -backend-config="encrypt=True": a
[31m│[0m [0mbool is required; to convert from string, use lowercase "true"
[31m╵[0m[0m
[31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mFailed to read file[0m
[31m│[0m [0m
[31m│[0m [0m[0mThe file "infrastructure/terraform/vpc/backend.hcl" could not be read.
[31m╵[0m[0m

2025-05-30 04:25:47,104 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:25:47,104 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:25:47,105 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:25:48,926 - utils.terraform_manager - ERROR - Command failed: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:25:48,926 - utils.terraform_manager - ERROR - Error: [31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mInvalid backend configuration value[0m
[31m│[0m [0m
[31m│[0m [0m[0mInvalid backend configuration argument -backend-config="encrypt=True": a
[31m│[0m [0mbool is required; to convert from string, use lowercase "true"
[31m╵[0m[0m

2025-05-30 04:25:48,927 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:27:05,001 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:27:05,001 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:27:05,002 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:27:06,577 - utils.terraform_manager - ERROR - Command failed: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:27:06,577 - utils.terraform_manager - ERROR - Error: [31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mInvalid backend configuration value[0m
[31m│[0m [0m
[31m│[0m [0m[0mInvalid backend configuration argument -backend-config="encrypt=True": a
[31m│[0m [0mbool is required; to convert from string, use lowercase "true"
[31m╵[0m[0m

2025-05-30 04:27:06,577 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:27:27,527 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:27:27,527 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:27:27,528 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:27:28,915 - utils.terraform_manager - ERROR - Command failed: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:27:28,916 - utils.terraform_manager - ERROR - Error: [31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mInvalid backend configuration value[0m
[31m│[0m [0m
[31m│[0m [0m[0mInvalid backend configuration argument -backend-config="encrypt=True": a
[31m│[0m [0mbool is required; to convert from string, use lowercase "true"
[31m╵[0m[0m

2025-05-30 04:27:28,916 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:28:01,487 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:28:01,487 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:28:01,487 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:28:04,861 - utils.terraform_manager - ERROR - Command failed: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:28:04,861 - utils.terraform_manager - ERROR - Error: [31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mFailed to get existing workspaces: S3 bucket "ce-capstone-terraform-state-dev" does not exist.
[31m│[0m [0m
[31m│[0m [0mThe referenced S3 bucket must have been previously created. If the S3 bucket
[31m│[0m [0mwas created within the last minute, please wait for a minute or two and try
[31m│[0m [0magain.
[31m│[0m [0m
[31m│[0m [0mError: operation error S3: ListObjectsV2, https response error StatusCode: 404, RequestID: NKVP6S30PQ9MQ4J4, HostID: 7HBxpymfqMb8hQg1K5nEUYMJBIYNsrQm8BvleobObE03BwQM/BzjA4JR4WHs03WLydCvdmXg/bE=, NoSuchBucket: 
[31m│[0m [0m[0m
[31m│[0m [0m
[31m│[0m [0m[0m
[31m╵[0m[0m

2025-05-30 04:28:04,861 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:35:35,203 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:35:35,203 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:35:35,214 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:35:38,010 - utils.aws_manager - INFO - S3 backend bucket created: ce-capstone-terraform-state-dev
2025-05-30 04:35:38,010 - utils.aws_manager - ERROR - Error creating DynamoDB table: 'AWSManager' object has no attribute 'dynamodb'
2025-05-30 04:35:38,012 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:35:49,260 - utils.terraform_manager - INFO - Running command: terraform workspace list
2025-05-30 04:35:51,411 - utils.terraform_manager - INFO - Running command: terraform workspace new dev
2025-05-30 04:35:54,740 - utils.terraform_manager - ERROR - Command failed: terraform workspace new dev
2025-05-30 04:35:54,741 - utils.terraform_manager - ERROR - Error: [31mfailed to lock s3 state: operation error DynamoDB: PutItem, https response error StatusCode: 400, RequestID: 098E7AQVTDEGML8N8D9OLOFSF3VV4KQNSO5AEMVJF66Q9ASUAAJG, ResourceNotFoundException: Requested resource not found
Unable to retrieve item from DynamoDB table "ce-capstone-terraform-locks-dev": operation error DynamoDB: GetItem, https response error StatusCode: 400, RequestID: 51579GAL4LC8S8HR45S1LSQNT7VV4KQNSO5AEMVJF66Q9ASUAAJG, ResourceNotFoundException: Requested resource not found[0m[0m

2025-05-30 04:35:54,741 - utils.terraform_manager - ERROR - Failed to create workspace dev: [31mfailed to lock s3 state: operation error DynamoDB: PutItem, https response error StatusCode: 400, RequestID: 098E7AQVTDEGML8N8D9OLOFSF3VV4KQNSO5AEMVJF66Q9ASUAAJG, ResourceNotFoundException: Requested resource not found
Unable to retrieve item from DynamoDB table "ce-capstone-terraform-locks-dev": operation error DynamoDB: GetItem, https response error StatusCode: 400, RequestID: 51579GAL4LC8S8HR45S1LSQNT7VV4KQNSO5AEMVJF66Q9ASUAAJG, ResourceNotFoundException: Requested resource not found[0m[0m

2025-05-30 04:35:54,741 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:36:37,837 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:36:37,837 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:36:37,847 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:36:39,991 - utils.aws_manager - INFO - S3 backend bucket created: ce-capstone-terraform-state-dev
2025-05-30 04:36:46,479 - utils.aws_manager - INFO - DynamoDB table created: ce-capstone-terraform-locks-dev
2025-05-30 04:36:46,480 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:36:51,308 - utils.terraform_manager - INFO - Running command: terraform workspace list
2025-05-30 04:36:53,459 - utils.terraform_manager - INFO - Running command: terraform workspace new dev
2025-05-30 04:36:59,100 - utils.terraform_manager - INFO - Running command: terraform workspace select dev
2025-05-30 04:37:01,279 - utils.terraform_manager - INFO - Running command: terraform validate
2025-05-30 04:37:02,378 - utils.terraform_manager - INFO - Running command: terraform plan -detailed-exitcode -var-file infrastructure/terraform/vpc/terraform.tfvars
2025-05-30 04:37:03,824 - utils.terraform_manager - ERROR - Command failed: terraform plan -detailed-exitcode -var-file infrastructure/terraform/vpc/terraform.tfvars
2025-05-30 04:37:03,824 - utils.terraform_manager - ERROR - Error: [31m╷[0m[0m
[31m│[0m [0m[1m[31mError: [0m[0m[1mFailed to read variables file[0m
[31m│[0m [0m
[31m│[0m [0m[0mGiven variables file infrastructure/terraform/vpc/terraform.tfvars does not
[31m│[0m [0mexist.
[31m╵[0m[0m

2025-05-30 04:37:03,824 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:37:49,734 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:37:49,734 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:37:49,744 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:37:51,833 - utils.aws_manager - INFO - S3 backend bucket created: ce-capstone-terraform-state-dev
2025-05-30 04:37:52,790 - utils.aws_manager - INFO - DynamoDB table already exists: ce-capstone-terraform-locks-dev
2025-05-30 04:37:52,792 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:37:58,030 - utils.terraform_manager - INFO - Running command: terraform workspace list
2025-05-30 04:38:00,199 - utils.terraform_manager - INFO - Running command: terraform workspace select dev
2025-05-30 04:38:02,832 - utils.terraform_manager - INFO - Running command: terraform validate
2025-05-30 04:38:03,867 - utils.terraform_manager - INFO - Running command: terraform plan -detailed-exitcode -var-file terraform.tfvars
2025-05-30 04:38:13,411 - utils.terraform_manager - ERROR - Command failed: terraform plan -detailed-exitcode -var-file terraform.tfvars
2025-05-30 04:38:13,411 - utils.terraform_manager - ERROR - Error: 
2025-05-30 04:38:13,412 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:39:33,317 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:39:33,317 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:39:33,327 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:39:35,432 - utils.aws_manager - INFO - S3 backend bucket created: ce-capstone-terraform-state-dev
2025-05-30 04:39:36,386 - utils.aws_manager - INFO - DynamoDB table already exists: ce-capstone-terraform-locks-dev
2025-05-30 04:39:36,388 - utils.terraform_manager - INFO - Running command: terraform init -upgrade -backend-config bucket=ce-capstone-terraform-state-dev -backend-config key=vpc/terraform.tfstate -backend-config region=us-east-1 -backend-config encrypt=true -backend-config dynamodb_table=ce-capstone-terraform-locks-dev
2025-05-30 04:39:41,473 - utils.terraform_manager - INFO - Running command: terraform workspace list
2025-05-30 04:39:43,637 - utils.terraform_manager - INFO - Running command: terraform workspace select dev
2025-05-30 04:39:45,830 - utils.terraform_manager - INFO - Running command: terraform validate
2025-05-30 04:39:53,269 - utils.terraform_manager - INFO - Running command: terraform apply -auto-approve -var-file terraform.tfvars
2025-05-30 04:40:02,446 - utils.terraform_manager - ERROR - Command failed: terraform apply -auto-approve -var-file terraform.tfvars
2025-05-30 04:40:02,446 - utils.terraform_manager - ERROR - Error: 
2025-05-30 04:40:02,446 - __main__ - ERROR - Unexpected error: EOF when reading a line
2025-05-30 04:40:15,552 - utils.config_handler - INFO - Configuration loaded from configs/dev.yaml
2025-05-30 04:40:15,552 - __main__ - INFO - Capstone Deployer initialized for environment: dev
2025-05-30 04:40:15,562 - botocore.credentials - INFO - Found credentials in shared credentials file: ~/.aws/credentials
2025-05-30 04:40:17,950 - __main__ - ERROR - Unexpected error: EOF when reading a line
