"""
CI/CD Integration Manager
Handles CI/CD pipeline integration and automation workflows.
"""

import os
import yaml
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path


class CICDManager:
    """
    Manages CI/CD pipeline integration for the capstone project.
    Generates pipeline configurations for various CI/CD platforms.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize CI/CD manager.
        
        Args:
            config: CI/CD configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.environment = config.get("environment", "dev")
        self.project_name = config.get("project_name", "ce-capstone-grp1")
    
    def generate_github_actions_workflow(self) -> str:
        """
        Generate GitHub Actions workflow file.
        
        Returns:
            str: Path to generated workflow file
        """
        try:
            workflow_dir = Path(".github/workflows")
            workflow_dir.mkdir(parents=True, exist_ok=True)
            
            workflow = {
                "name": "Capstone Infrastructure Deployment",
                "on": {
                    "push": {
                        "branches": ["main", "develop"]
                    },
                    "pull_request": {
                        "branches": ["main"]
                    },
                    "workflow_dispatch": {
                        "inputs": {
                            "environment": {
                                "description": "Environment to deploy",
                                "required": True,
                                "default": "dev",
                                "type": "choice",
                                "options": ["dev", "staging", "prod"]
                            },
                            "action": {
                                "description": "Action to perform",
                                "required": True,
                                "default": "deploy",
                                "type": "choice",
                                "options": ["deploy", "destroy", "plan"]
                            }
                        }
                    }
                },
                "env": {
                    "AWS_REGION": "us-west-2",
                    "TERRAFORM_VERSION": "1.6.0",
                    "KUBECTL_VERSION": "1.28.0"
                },
                "jobs": {
                    "security-scan": {
                        "runs-on": "ubuntu-latest",
                        "steps": [
                            {
                                "name": "Checkout code",
                                "uses": "actions/checkout@v4"
                            },
                            {
                                "name": "Setup Python",
                                "uses": "actions/setup-python@v4",
                                "with": {
                                    "python-version": "3.9"
                                }
                            },
                            {
                                "name": "Install dependencies",
                                "run": "pip install -r requirements.txt"
                            },
                            {
                                "name": "Run security scan",
                                "run": "python deploy.py --security-scan-only"
                            }
                        ]
                    },
                    "terraform-plan": {
                        "runs-on": "ubuntu-latest",
                        "needs": "security-scan",
                        "strategy": {
                            "matrix": {
                                "component": ["vpc", "eks"]
                            }
                        },
                        "steps": [
                            {
                                "name": "Checkout code",
                                "uses": "actions/checkout@v4"
                            },
                            {
                                "name": "Configure AWS credentials",
                                "uses": "aws-actions/configure-aws-credentials@v4",
                                "with": {
                                    "aws-access-key-id": "${{ secrets.AWS_ACCESS_KEY_ID }}",
                                    "aws-secret-access-key": "${{ secrets.AWS_SECRET_ACCESS_KEY }}",
                                    "aws-region": "${{ env.AWS_REGION }}"
                                }
                            },
                            {
                                "name": "Setup Terraform",
                                "uses": "hashicorp/setup-terraform@v3",
                                "with": {
                                    "terraform_version": "${{ env.TERRAFORM_VERSION }}"
                                }
                            },
                            {
                                "name": "Setup Python",
                                "uses": "actions/setup-python@v4",
                                "with": {
                                    "python-version": "3.9"
                                }
                            },
                            {
                                "name": "Install dependencies",
                                "run": "pip install -r requirements.txt"
                            },
                            {
                                "name": "Terraform Plan",
                                "run": f"python deploy.py --environment ${{{{ github.event.inputs.environment || 'dev' }}}} --component ${{{{ matrix.component }}}} --plan-only"
                            }
                        ]
                    },
                    "deploy": {
                        "runs-on": "ubuntu-latest",
                        "needs": ["security-scan", "terraform-plan"],
                        "if": "github.event.inputs.action == 'deploy' || github.ref == 'refs/heads/main'",
                        "steps": [
                            {
                                "name": "Checkout code",
                                "uses": "actions/checkout@v4"
                            },
                            {
                                "name": "Configure AWS credentials",
                                "uses": "aws-actions/configure-aws-credentials@v4",
                                "with": {
                                    "aws-access-key-id": "${{ secrets.AWS_ACCESS_KEY_ID }}",
                                    "aws-secret-access-key": "${{ secrets.AWS_SECRET_ACCESS_KEY }}",
                                    "aws-region": "${{ env.AWS_REGION }}"
                                }
                            },
                            {
                                "name": "Setup Terraform",
                                "uses": "hashicorp/setup-terraform@v3",
                                "with": {
                                    "terraform_version": "${{ env.TERRAFORM_VERSION }}"
                                }
                            },
                            {
                                "name": "Setup kubectl",
                                "uses": "azure/setup-kubectl@v3",
                                "with": {
                                    "version": "${{ env.KUBECTL_VERSION }}"
                                }
                            },
                            {
                                "name": "Setup Python",
                                "uses": "actions/setup-python@v4",
                                "with": {
                                    "python-version": "3.9"
                                }
                            },
                            {
                                "name": "Install dependencies",
                                "run": "pip install -r requirements.txt"
                            },
                            {
                                "name": "Deploy Infrastructure",
                                "run": f"python deploy.py --environment ${{{{ github.event.inputs.environment || 'dev' }}}} --full-stack"
                            },
                            {
                                "name": "Run post-deployment tests",
                                "run": "python deploy.py --test-deployment"
                            }
                        ]
                    },
                    "destroy": {
                        "runs-on": "ubuntu-latest",
                        "if": "github.event.inputs.action == 'destroy'",
                        "steps": [
                            {
                                "name": "Checkout code",
                                "uses": "actions/checkout@v4"
                            },
                            {
                                "name": "Configure AWS credentials",
                                "uses": "aws-actions/configure-aws-credentials@v4",
                                "with": {
                                    "aws-access-key-id": "${{ secrets.AWS_ACCESS_KEY_ID }}",
                                    "aws-secret-access-key": "${{ secrets.AWS_SECRET_ACCESS_KEY }}",
                                    "aws-region": "${{ env.AWS_REGION }}"
                                }
                            },
                            {
                                "name": "Setup Terraform",
                                "uses": "hashicorp/setup-terraform@v3",
                                "with": {
                                    "terraform_version": "${{ env.TERRAFORM_VERSION }}"
                                }
                            },
                            {
                                "name": "Setup Python",
                                "uses": "actions/setup-python@v4",
                                "with": {
                                    "python-version": "3.9"
                                }
                            },
                            {
                                "name": "Install dependencies",
                                "run": "pip install -r requirements.txt"
                            },
                            {
                                "name": "Destroy Infrastructure",
                                "run": f"python deploy.py --environment ${{{{ github.event.inputs.environment }}}} --destroy"
                            }
                        ]
                    }
                }
            }
            
            workflow_file = workflow_dir / "deploy.yml"
            with open(workflow_file, 'w') as f:
                yaml.dump(workflow, f, default_flow_style=False, indent=2)
            
            print(f"✅ GitHub Actions workflow created: {workflow_file}")
            return str(workflow_file)
            
        except Exception as e:
            self.logger.error(f"Failed to generate GitHub Actions workflow: {e}")
            print(f"❌ Failed to generate GitHub Actions workflow: {e}")
            return ""
    
    def generate_gitlab_ci_pipeline(self) -> str:
        """
        Generate GitLab CI pipeline file.
        
        Returns:
            str: Path to generated pipeline file
        """
        try:
            pipeline = {
                "stages": ["validate", "plan", "deploy", "test", "cleanup"],
                "variables": {
                    "AWS_DEFAULT_REGION": "us-west-2",
                    "TERRAFORM_VERSION": "1.6.0",
                    "KUBECTL_VERSION": "1.28.0"
                },
                "before_script": [
                    "apt-get update -qq && apt-get install -y -qq git curl python3 python3-pip",
                    "pip3 install -r requirements.txt",
                    "curl -LO https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip",
                    "unzip terraform_${TERRAFORM_VERSION}_linux_amd64.zip",
                    "mv terraform /usr/local/bin/",
                    "curl -LO https://dl.k8s.io/release/v${KUBECTL_VERSION}/bin/linux/amd64/kubectl",
                    "chmod +x kubectl",
                    "mv kubectl /usr/local/bin/"
                ],
                "validate": {
                    "stage": "validate",
                    "script": [
                        "python3 deploy.py --check-dependencies",
                        "python3 deploy.py --security-scan-only"
                    ],
                    "rules": [
                        {"if": "$CI_PIPELINE_SOURCE == 'merge_request_event'"},
                        {"if": "$CI_COMMIT_BRANCH == 'main'"}
                    ]
                },
                "plan:vpc": {
                    "stage": "plan",
                    "script": [
                        "python3 deploy.py --environment ${ENVIRONMENT:-dev} --component vpc --plan-only"
                    ],
                    "artifacts": {
                        "paths": ["infrastructure/terraform/vpc/terraform.plan"],
                        "expire_in": "1 hour"
                    },
                    "rules": [
                        {"if": "$CI_PIPELINE_SOURCE == 'merge_request_event'"},
                        {"if": "$CI_COMMIT_BRANCH == 'main'"}
                    ]
                },
                "plan:eks": {
                    "stage": "plan",
                    "script": [
                        "python3 deploy.py --environment ${ENVIRONMENT:-dev} --component eks --plan-only"
                    ],
                    "artifacts": {
                        "paths": ["infrastructure/terraform/eks/terraform.plan"],
                        "expire_in": "1 hour"
                    },
                    "rules": [
                        {"if": "$CI_PIPELINE_SOURCE == 'merge_request_event'"},
                        {"if": "$CI_COMMIT_BRANCH == 'main'"}
                    ]
                },
                "deploy:dev": {
                    "stage": "deploy",
                    "script": [
                        "python3 deploy.py --environment dev --full-stack"
                    ],
                    "rules": [
                        {"if": "$CI_COMMIT_BRANCH == 'develop'"}
                    ],
                    "environment": {
                        "name": "development",
                        "url": "https://dev.capstone.example.com"
                    }
                },
                "deploy:staging": {
                    "stage": "deploy",
                    "script": [
                        "python3 deploy.py --environment staging --full-stack"
                    ],
                    "rules": [
                        {"if": "$CI_COMMIT_BRANCH == 'main'"}
                    ],
                    "environment": {
                        "name": "staging",
                        "url": "https://staging.capstone.example.com"
                    }
                },
                "deploy:prod": {
                    "stage": "deploy",
                    "script": [
                        "python3 deploy.py --environment prod --full-stack"
                    ],
                    "rules": [
                        {"if": "$CI_COMMIT_TAG"}
                    ],
                    "when": "manual",
                    "environment": {
                        "name": "production",
                        "url": "https://capstone.example.com"
                    }
                },
                "test": {
                    "stage": "test",
                    "script": [
                        "python3 deploy.py --test-deployment",
                        "python3 deploy.py --security-scan"
                    ],
                    "rules": [
                        {"if": "$CI_COMMIT_BRANCH == 'main'"},
                        {"if": "$CI_COMMIT_TAG"}
                    ]
                },
                "cleanup:dev": {
                    "stage": "cleanup",
                    "script": [
                        "python3 deploy.py --environment dev --destroy"
                    ],
                    "rules": [
                        {"if": "$CLEANUP_DEV == 'true'"}
                    ],
                    "when": "manual"
                }
            }
            
            pipeline_file = Path(".gitlab-ci.yml")
            with open(pipeline_file, 'w') as f:
                yaml.dump(pipeline, f, default_flow_style=False, indent=2)
            
            print(f"✅ GitLab CI pipeline created: {pipeline_file}")
            return str(pipeline_file)
            
        except Exception as e:
            self.logger.error(f"Failed to generate GitLab CI pipeline: {e}")
            print(f"❌ Failed to generate GitLab CI pipeline: {e}")
            return ""
    
    def generate_jenkins_pipeline(self) -> str:
        """
        Generate Jenkins pipeline file.
        
        Returns:
            str: Path to generated pipeline file
        """
        try:
            pipeline_content = '''pipeline {
    agent any

    parameters {
        choice(
            name: 'ENVIRONMENT',
            choices: ['dev', 'staging', 'prod'],
            description: 'Environment to deploy'
        )
        choice(
            name: 'ACTION',
            choices: ['deploy', 'destroy', 'plan'],
            description: 'Action to perform'
        )
    }

    environment {
        AWS_DEFAULT_REGION = 'us-west-2'
        TERRAFORM_VERSION = '1.6.0'
        KUBECTL_VERSION = '1.28.0'
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }

        stage('Setup') {
            steps {
                sh """
                    # Install Python dependencies
                    pip3 install -r requirements.txt

                    # Install Terraform
                    wget https://releases.hashicorp.com/terraform/\${TERRAFORM_VERSION}/terraform_\${TERRAFORM_VERSION}_linux_amd64.zip
                    unzip terraform_\${TERRAFORM_VERSION}_linux_amd64.zip
                    sudo mv terraform /usr/local/bin/

                    # Install kubectl
                    curl -LO https://dl.k8s.io/release/v\${KUBECTL_VERSION}/bin/linux/amd64/kubectl
                    chmod +x kubectl
                    sudo mv kubectl /usr/local/bin/
                """
            }
        }
        
        stage('Validate') {
            steps {
                sh """
                    python3 deploy.py --check-dependencies
                    python3 deploy.py --security-scan-only
                """
            }
        }
        
        stage('Plan') {
            when {
                expression { params.ACTION == 'plan' || params.ACTION == 'deploy' }
            }
            parallel {
                stage('Plan VPC') {
                    steps {
                        sh 'python3 deploy.py --environment ${ENVIRONMENT} --component vpc --plan-only'
                    }
                }
                stage('Plan EKS') {
                    steps {
                        sh 'python3 deploy.py --environment ${ENVIRONMENT} --component eks --plan-only'
                    }
                }
            }
        }
        
        stage('Deploy') {
            when {
                expression { params.ACTION == 'deploy' }
            }
            steps {
                sh 'python3 deploy.py --environment ${ENVIRONMENT} --full-stack'
            }
        }
        
        stage('Test') {
            when {
                expression { params.ACTION == 'deploy' }
            }
            steps {
                sh """
                    python3 deploy.py --test-deployment
                    python3 deploy.py --security-scan
                """
            }
        }
        
        stage('Destroy') {
            when {
                expression { params.ACTION == 'destroy' }
            }
            steps {
                input message: 'Are you sure you want to destroy the infrastructure?', ok: 'Destroy'
                sh 'python3 deploy.py --environment ${ENVIRONMENT} --destroy'
            }
        }
    }
    
    post {
        always {
            archiveArtifacts artifacts: 'logs/*.log', allowEmptyArchive: true
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'reports',
                reportFiles: '*.html',
                reportName: 'Deployment Report'
            ])
        }
        failure {
            emailext (
                subject: "Deployment Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The deployment pipeline failed. Please check the logs for details.",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        success {
            emailext (
                subject: "Deployment Successful: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The deployment pipeline completed successfully.",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
'''
            
            pipeline_file = Path("Jenkinsfile")
            with open(pipeline_file, 'w') as f:
                f.write(pipeline_content.strip())
            
            print(f"✅ Jenkins pipeline created: {pipeline_file}")
            return str(pipeline_file)
            
        except Exception as e:
            self.logger.error(f"Failed to generate Jenkins pipeline: {e}")
            print(f"❌ Failed to generate Jenkins pipeline: {e}")
            return ""
    
    def generate_all_pipelines(self) -> Dict[str, str]:
        """
        Generate all CI/CD pipeline configurations.
        
        Returns:
            Dict: Mapping of platform to generated file path
        """
        print("\n🔄 Generating CI/CD pipeline configurations...")
        
        pipelines = {}
        
        # GitHub Actions
        github_file = self.generate_github_actions_workflow()
        if github_file:
            pipelines["github_actions"] = github_file
        
        # GitLab CI
        gitlab_file = self.generate_gitlab_ci_pipeline()
        if gitlab_file:
            pipelines["gitlab_ci"] = gitlab_file
        
        # Jenkins
        jenkins_file = self.generate_jenkins_pipeline()
        if jenkins_file:
            pipelines["jenkins"] = jenkins_file
        
        print(f"✅ Generated {len(pipelines)} CI/CD pipeline configurations")
        return pipelines
    
    def create_deployment_scripts(self) -> List[str]:
        """
        Create deployment helper scripts.
        
        Returns:
            List: Paths to created scripts
        """
        scripts = []
        
        # Quick deploy script
        deploy_script = Path("scripts/quick-deploy.sh")
        deploy_script.parent.mkdir(exist_ok=True)
        
        deploy_content = '''#!/bin/bash
set -e

ENVIRONMENT=${1:-dev}
ACTION=${2:-deploy}

echo "🚀 Capstone Quick Deploy Script"
echo "Environment: $ENVIRONMENT"
echo "Action: $ACTION"

# Check dependencies
python3 deploy.py --check-dependencies

# Perform action
case $ACTION in
    "deploy")
        python3 deploy.py --environment $ENVIRONMENT --full-stack
        ;;
    "destroy")
        echo "⚠️ WARNING: This will destroy all resources!"
        read -p "Type 'DESTROY' to confirm: " confirm
        if [ "$confirm" = "DESTROY" ]; then
            python3 deploy.py --environment $ENVIRONMENT --destroy
        else
            echo "❌ Destruction cancelled"
            exit 1
        fi
        ;;
    "status")
        python3 deploy.py --environment $ENVIRONMENT --status
        ;;
    *)
        echo "❌ Unknown action: $ACTION"
        echo "Usage: $0 [environment] [deploy|destroy|status]"
        exit 1
        ;;
esac
'''
        
        with open(deploy_script, 'w') as f:
            f.write(deploy_content.strip())
        
        deploy_script.chmod(0o755)
        scripts.append(str(deploy_script))
        
        print(f"✅ Created deployment scripts: {len(scripts)} files")
        return scripts
