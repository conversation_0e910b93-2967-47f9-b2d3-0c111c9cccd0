"""
Monitoring Manager <PERSON><PERSON><PERSON>les monitoring setup and health checks for the infrastructure.
"""

import boto3
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta


class MonitoringManager:
    """
    Manages monitoring and alerting for the deployed infrastructure.
    Integrates with CloudWatch, Prometheus, and other monitoring tools.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize monitoring manager.
        
        Args:
            config: Monitoring configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.region = config.get("aws", {}).get("region", "us-west-2")
        
        # Initialize AWS clients
        try:
            self.cloudwatch = boto3.client('cloudwatch', region_name=self.region)
            self.logs = boto3.client('logs', region_name=self.region)
            self.ec2 = boto3.client('ec2', region_name=self.region)
            self.eks = boto3.client('eks', region_name=self.region)
        except Exception as e:
            self.logger.error(f"Failed to initialize AWS clients: {e}")
            raise
    
    def create_cloudwatch_dashboard(self, dashboard_name: str) -> bool:
        """
        Create CloudWatch dashboard for infrastructure monitoring.
        
        Args:
            dashboard_name: Name of the dashboard
            
        Returns:
            bool: True if successful
        """
        try:
            dashboard_body = {
                "widgets": [
                    {
                        "type": "metric",
                        "x": 0,
                        "y": 0,
                        "width": 12,
                        "height": 6,
                        "properties": {
                            "metrics": [
                                ["AWS/EKS", "cluster_failed_request_count", "ClusterName", self.config.get("cluster_name", "ce-capstone-dev")],
                                [".", "cluster_request_total", ".", "."]
                            ],
                            "period": 300,
                            "stat": "Sum",
                            "region": self.region,
                            "title": "EKS Cluster Metrics"
                        }
                    },
                    {
                        "type": "metric",
                        "x": 0,
                        "y": 6,
                        "width": 12,
                        "height": 6,
                        "properties": {
                            "metrics": [
                                ["AWS/EC2", "CPUUtilization", "InstanceId", "*"],
                                [".", "NetworkIn", ".", "*"],
                                [".", "NetworkOut", ".", "*"]
                            ],
                            "period": 300,
                            "stat": "Average",
                            "region": self.region,
                            "title": "EC2 Instance Metrics"
                        }
                    },
                    {
                        "type": "log",
                        "x": 0,
                        "y": 12,
                        "width": 24,
                        "height": 6,
                        "properties": {
                            "query": f"SOURCE '/aws/eks/{self.config.get('cluster_name', 'ce-capstone-dev')}/cluster'\n| fields @timestamp, @message\n| sort @timestamp desc\n| limit 100",
                            "region": self.region,
                            "title": "EKS Cluster Logs"
                        }
                    }
                ]
            }
            
            self.cloudwatch.put_dashboard(
                DashboardName=dashboard_name,
                DashboardBody=str(dashboard_body).replace("'", '"')
            )
            
            print(f"✅ CloudWatch dashboard '{dashboard_name}' created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create CloudWatch dashboard: {e}")
            print(f"❌ Failed to create CloudWatch dashboard: {e}")
            return False
    
    def create_cloudwatch_alarms(self) -> bool:
        """
        Create CloudWatch alarms for critical metrics.
        
        Returns:
            bool: True if successful
        """
        try:
            alarms = [
                {
                    "AlarmName": f"EKS-HighCPU-{self.config.get('environment', 'dev')}",
                    "ComparisonOperator": "GreaterThanThreshold",
                    "EvaluationPeriods": 2,
                    "MetricName": "CPUUtilization",
                    "Namespace": "AWS/EC2",
                    "Period": 300,
                    "Statistic": "Average",
                    "Threshold": 80.0,
                    "ActionsEnabled": True,
                    "AlarmDescription": "High CPU utilization on EKS nodes",
                    "Unit": "Percent"
                },
                {
                    "AlarmName": f"EKS-HighMemory-{self.config.get('environment', 'dev')}",
                    "ComparisonOperator": "GreaterThanThreshold",
                    "EvaluationPeriods": 2,
                    "MetricName": "MemoryUtilization",
                    "Namespace": "CWAgent",
                    "Period": 300,
                    "Statistic": "Average",
                    "Threshold": 85.0,
                    "ActionsEnabled": True,
                    "AlarmDescription": "High memory utilization on EKS nodes",
                    "Unit": "Percent"
                }
            ]
            
            for alarm in alarms:
                self.cloudwatch.put_metric_alarm(**alarm)
                print(f"✅ Created alarm: {alarm['AlarmName']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create CloudWatch alarms: {e}")
            print(f"❌ Failed to create CloudWatch alarms: {e}")
            return False
    
    def get_infrastructure_health(self) -> Dict[str, Any]:
        """
        Get comprehensive health status of the infrastructure.
        
        Returns:
            Dict: Health status information
        """
        health_status = {
            "overall_status": "healthy",
            "components": {},
            "alerts": [],
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # Check EKS cluster health
            cluster_name = self.config.get("cluster_name", "ce-capstone-dev")
            try:
                cluster = self.eks.describe_cluster(name=cluster_name)
                cluster_status = cluster['cluster']['status']
                
                health_status["components"]["eks_cluster"] = {
                    "status": "healthy" if cluster_status == "ACTIVE" else "unhealthy",
                    "details": f"Cluster status: {cluster_status}",
                    "version": cluster['cluster']['version']
                }
                
                if cluster_status != "ACTIVE":
                    health_status["overall_status"] = "degraded"
                    health_status["alerts"].append(f"EKS cluster is not active: {cluster_status}")
                    
            except Exception as e:
                health_status["components"]["eks_cluster"] = {
                    "status": "error",
                    "details": str(e)
                }
                health_status["overall_status"] = "unhealthy"
                health_status["alerts"].append(f"Cannot access EKS cluster: {e}")
            
            # Check EC2 instances (EKS nodes)
            try:
                instances = self.ec2.describe_instances(
                    Filters=[
                        {
                            'Name': 'tag:kubernetes.io/cluster/' + cluster_name,
                            'Values': ['owned']
                        },
                        {
                            'Name': 'instance-state-name',
                            'Values': ['running']
                        }
                    ]
                )
                
                running_instances = 0
                for reservation in instances['Reservations']:
                    running_instances += len(reservation['Instances'])
                
                health_status["components"]["eks_nodes"] = {
                    "status": "healthy" if running_instances > 0 else "unhealthy",
                    "details": f"Running instances: {running_instances}",
                    "count": running_instances
                }
                
                if running_instances == 0:
                    health_status["overall_status"] = "unhealthy"
                    health_status["alerts"].append("No EKS nodes are running")
                    
            except Exception as e:
                health_status["components"]["eks_nodes"] = {
                    "status": "error",
                    "details": str(e)
                }
                health_status["alerts"].append(f"Cannot check EKS nodes: {e}")
            
            # Check CloudWatch alarms
            try:
                alarms = self.cloudwatch.describe_alarms(
                    StateValue='ALARM',
                    MaxRecords=50
                )
                
                active_alarms = len(alarms['MetricAlarms'])
                health_status["components"]["cloudwatch_alarms"] = {
                    "status": "healthy" if active_alarms == 0 else "warning",
                    "details": f"Active alarms: {active_alarms}",
                    "count": active_alarms
                }
                
                if active_alarms > 0:
                    health_status["overall_status"] = "degraded"
                    for alarm in alarms['MetricAlarms']:
                        health_status["alerts"].append(f"Alarm: {alarm['AlarmName']} - {alarm['StateReason']}")
                        
            except Exception as e:
                health_status["components"]["cloudwatch_alarms"] = {
                    "status": "error",
                    "details": str(e)
                }
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"Error getting infrastructure health: {e}")
            return {
                "overall_status": "error",
                "components": {},
                "alerts": [f"Health check failed: {e}"],
                "timestamp": datetime.now().isoformat()
            }
    
    def display_health_status(self) -> None:
        """Display infrastructure health status in a readable format."""
        print("\n" + "=" * 60)
        print("           INFRASTRUCTURE HEALTH STATUS")
        print("=" * 60)
        
        health = self.get_infrastructure_health()
        
        # Overall status
        status_icon = {
            "healthy": "✅",
            "degraded": "⚠️",
            "unhealthy": "❌",
            "error": "💥"
        }.get(health["overall_status"], "❓")
        
        print(f"\n{status_icon} Overall Status: {health['overall_status'].upper()}")
        print(f"📅 Last Check: {health['timestamp']}")
        
        # Component status
        print(f"\n📊 Component Status:")
        for component, details in health["components"].items():
            comp_icon = {
                "healthy": "✅",
                "warning": "⚠️",
                "unhealthy": "❌",
                "error": "💥"
            }.get(details["status"], "❓")
            
            print(f"  {comp_icon} {component.replace('_', ' ').title()}: {details['status']}")
            print(f"     └─ {details['details']}")
        
        # Active alerts
        if health["alerts"]:
            print(f"\n🚨 Active Alerts ({len(health['alerts'])}):")
            for alert in health["alerts"]:
                print(f"  ⚠️ {alert}")
        else:
            print(f"\n🎉 No active alerts")
        
        print("=" * 60)
    
    def setup_log_groups(self) -> bool:
        """
        Setup CloudWatch log groups for the infrastructure.
        
        Returns:
            bool: True if successful
        """
        try:
            log_groups = [
                f"/aws/eks/{self.config.get('cluster_name', 'ce-capstone-dev')}/cluster",
                f"/capstone/{self.config.get('environment', 'dev')}/applications",
                f"/capstone/{self.config.get('environment', 'dev')}/infrastructure"
            ]
            
            for log_group in log_groups:
                try:
                    self.logs.create_log_group(
                        logGroupName=log_group,
                        retentionInDays=self.config.get("log_retention_days", 7)
                    )
                    print(f"✅ Created log group: {log_group}")
                except self.logs.exceptions.ResourceAlreadyExistsException:
                    print(f"ℹ️ Log group already exists: {log_group}")
                except Exception as e:
                    print(f"❌ Failed to create log group {log_group}: {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup log groups: {e}")
            return False
    
    def get_cost_metrics(self) -> Dict[str, Any]:
        """
        Get cost metrics for the deployed infrastructure.
        
        Returns:
            Dict: Cost information
        """
        try:
            # This would integrate with AWS Cost Explorer API
            # For now, return placeholder data
            return {
                "daily_cost": 25.50,
                "monthly_estimate": 765.00,
                "top_services": [
                    {"service": "Amazon EKS", "cost": 15.20},
                    {"service": "Amazon EC2", "cost": 8.30},
                    {"service": "Amazon VPC", "cost": 2.00}
                ],
                "currency": "USD",
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting cost metrics: {e}")
            return {"error": str(e)}
    
    def cleanup_monitoring(self) -> bool:
        """
        Cleanup monitoring resources.
        
        Returns:
            bool: True if successful
        """
        try:
            # Delete CloudWatch dashboard
            dashboard_name = f"capstone-{self.config.get('environment', 'dev')}"
            try:
                self.cloudwatch.delete_dashboards(DashboardNames=[dashboard_name])
                print(f"✅ Deleted dashboard: {dashboard_name}")
            except Exception as e:
                print(f"ℹ️ Dashboard not found or already deleted: {e}")
            
            # Delete alarms
            alarm_names = [
                f"EKS-HighCPU-{self.config.get('environment', 'dev')}",
                f"EKS-HighMemory-{self.config.get('environment', 'dev')}"
            ]
            
            try:
                self.cloudwatch.delete_alarms(AlarmNames=alarm_names)
                print(f"✅ Deleted {len(alarm_names)} alarms")
            except Exception as e:
                print(f"ℹ️ Some alarms not found: {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error during monitoring cleanup: {e}")
            return False
