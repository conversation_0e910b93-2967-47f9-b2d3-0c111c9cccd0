"""
Backup and Disaster Recovery Manager
Handles backup operations and disaster recovery procedures.
"""

import boto3
import json
import logging
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path


class BackupManager:
    """
    Manages backup and disaster recovery operations for the infrastructure.
    Handles Terraform state backups, EKS cluster backups, and application data.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize backup manager.
        
        Args:
            config: Backup configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.region = config.get("aws", {}).get("region", "us-west-2")
        self.environment = config.get("environment", "dev")
        
        # Initialize AWS clients
        try:
            self.s3 = boto3.client('s3', region_name=self.region)
            self.eks = boto3.client('eks', region_name=self.region)
            self.ec2 = boto3.client('ec2', region_name=self.region)
        except Exception as e:
            self.logger.error(f"Failed to initialize AWS clients: {e}")
            raise
    
    def create_backup_bucket(self, bucket_name: str) -> bool:
        """
        Create S3 bucket for backups if it doesn't exist.
        
        Args:
            bucket_name: Name of the backup bucket
            
        Returns:
            bool: True if successful
        """
        try:
            # Check if bucket exists
            try:
                self.s3.head_bucket(Bucket=bucket_name)
                print(f"ℹ️ Backup bucket already exists: {bucket_name}")
                return True
            except:
                pass
            
            # Create bucket
            if self.region == 'us-east-1':
                self.s3.create_bucket(Bucket=bucket_name)
            else:
                self.s3.create_bucket(
                    Bucket=bucket_name,
                    CreateBucketConfiguration={'LocationConstraint': self.region}
                )
            
            # Enable versioning
            self.s3.put_bucket_versioning(
                Bucket=bucket_name,
                VersioningConfiguration={'Status': 'Enabled'}
            )
            
            # Enable encryption
            self.s3.put_bucket_encryption(
                Bucket=bucket_name,
                ServerSideEncryptionConfiguration={
                    'Rules': [
                        {
                            'ApplyServerSideEncryptionByDefault': {
                                'SSEAlgorithm': 'AES256'
                            }
                        }
                    ]
                }
            )
            
            # Set lifecycle policy
            lifecycle_config = {
                'Rules': [
                    {
                        'ID': 'backup-lifecycle',
                        'Status': 'Enabled',
                        'Filter': {'Prefix': 'backups/'},
                        'Transitions': [
                            {
                                'Days': 30,
                                'StorageClass': 'STANDARD_IA'
                            },
                            {
                                'Days': 90,
                                'StorageClass': 'GLACIER'
                            }
                        ],
                        'Expiration': {
                            'Days': 365
                        }
                    }
                ]
            }
            
            self.s3.put_bucket_lifecycle_configuration(
                Bucket=bucket_name,
                LifecycleConfiguration=lifecycle_config
            )
            
            print(f"✅ Created backup bucket: {bucket_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create backup bucket: {e}")
            print(f"❌ Failed to create backup bucket: {e}")
            return False
    
    def backup_terraform_state(self, state_bucket: str, backup_bucket: str) -> bool:
        """
        Backup Terraform state files to backup bucket.
        
        Args:
            state_bucket: Source state bucket
            backup_bucket: Destination backup bucket
            
        Returns:
            bool: True if successful
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            
            # List all state files
            response = self.s3.list_objects_v2(Bucket=state_bucket)
            
            if 'Contents' not in response:
                print("ℹ️ No state files found to backup")
                return True
            
            backed_up_files = 0
            for obj in response['Contents']:
                source_key = obj['Key']
                backup_key = f"backups/terraform-state/{timestamp}/{source_key}"
                
                # Copy state file to backup bucket
                copy_source = {'Bucket': state_bucket, 'Key': source_key}
                self.s3.copy_object(
                    CopySource=copy_source,
                    Bucket=backup_bucket,
                    Key=backup_key
                )
                
                backed_up_files += 1
                print(f"✅ Backed up: {source_key} -> {backup_key}")
            
            # Create backup manifest
            manifest = {
                "backup_type": "terraform_state",
                "timestamp": timestamp,
                "source_bucket": state_bucket,
                "backup_bucket": backup_bucket,
                "files_backed_up": backed_up_files,
                "environment": self.environment
            }
            
            manifest_key = f"backups/terraform-state/{timestamp}/manifest.json"
            self.s3.put_object(
                Bucket=backup_bucket,
                Key=manifest_key,
                Body=json.dumps(manifest, indent=2)
            )
            
            print(f"✅ Terraform state backup completed: {backed_up_files} files")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to backup Terraform state: {e}")
            print(f"❌ Failed to backup Terraform state: {e}")
            return False
    
    def backup_kubernetes_resources(self, cluster_name: str, backup_bucket: str) -> bool:
        """
        Backup Kubernetes resources and configurations.
        
        Args:
            cluster_name: Name of the EKS cluster
            backup_bucket: Backup bucket name
            
        Returns:
            bool: True if successful
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            backup_dir = Path(f"/tmp/k8s-backup-{timestamp}")
            backup_dir.mkdir(exist_ok=True)
            
            # Update kubeconfig
            subprocess.run([
                "aws", "eks", "update-kubeconfig",
                "--region", self.region,
                "--name", cluster_name
            ], check=True, capture_output=True)
            
            # Backup different resource types
            resources_to_backup = [
                "deployments",
                "services",
                "configmaps",
                "secrets",
                "ingresses",
                "persistentvolumes",
                "persistentvolumeclaims",
                "namespaces"
            ]
            
            backed_up_resources = []
            
            for resource_type in resources_to_backup:
                try:
                    # Get all resources of this type
                    result = subprocess.run([
                        "kubectl", "get", resource_type,
                        "--all-namespaces",
                        "-o", "yaml"
                    ], capture_output=True, text=True, check=True)
                    
                    if result.stdout.strip():
                        resource_file = backup_dir / f"{resource_type}.yaml"
                        with open(resource_file, 'w') as f:
                            f.write(result.stdout)
                        
                        backed_up_resources.append(resource_type)
                        print(f"✅ Backed up {resource_type}")
                    
                except subprocess.CalledProcessError as e:
                    print(f"⚠️ Could not backup {resource_type}: {e}")
            
            # Create backup archive
            archive_path = f"/tmp/k8s-backup-{timestamp}.tar.gz"
            subprocess.run([
                "tar", "-czf", archive_path,
                "-C", str(backup_dir.parent),
                backup_dir.name
            ], check=True)
            
            # Upload to S3
            backup_key = f"backups/kubernetes/{timestamp}/k8s-resources.tar.gz"
            with open(archive_path, 'rb') as f:
                self.s3.put_object(
                    Bucket=backup_bucket,
                    Key=backup_key,
                    Body=f.read()
                )
            
            # Create backup manifest
            manifest = {
                "backup_type": "kubernetes_resources",
                "timestamp": timestamp,
                "cluster_name": cluster_name,
                "backup_bucket": backup_bucket,
                "resources_backed_up": backed_up_resources,
                "environment": self.environment
            }
            
            manifest_key = f"backups/kubernetes/{timestamp}/manifest.json"
            self.s3.put_object(
                Bucket=backup_bucket,
                Key=manifest_key,
                Body=json.dumps(manifest, indent=2)
            )
            
            # Cleanup local files
            subprocess.run(["rm", "-rf", str(backup_dir), archive_path])
            
            print(f"✅ Kubernetes backup completed: {len(backed_up_resources)} resource types")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to backup Kubernetes resources: {e}")
            print(f"❌ Failed to backup Kubernetes resources: {e}")
            return False
    
    def create_ebs_snapshots(self, cluster_name: str) -> bool:
        """
        Create EBS snapshots for EKS node volumes.
        
        Args:
            cluster_name: Name of the EKS cluster
            
        Returns:
            bool: True if successful
        """
        try:
            # Find EKS node instances
            instances = self.ec2.describe_instances(
                Filters=[
                    {
                        'Name': 'tag:kubernetes.io/cluster/' + cluster_name,
                        'Values': ['owned']
                    },
                    {
                        'Name': 'instance-state-name',
                        'Values': ['running']
                    }
                ]
            )
            
            snapshots_created = 0
            
            for reservation in instances['Reservations']:
                for instance in reservation['Instances']:
                    instance_id = instance['InstanceId']
                    
                    # Get volumes attached to this instance
                    for block_device in instance.get('BlockDeviceMappings', []):
                        volume_id = block_device['Ebs']['VolumeId']
                        
                        # Create snapshot
                        snapshot = self.ec2.create_snapshot(
                            VolumeId=volume_id,
                            Description=f"Backup of {volume_id} from EKS node {instance_id}",
                            TagSpecifications=[
                                {
                                    'ResourceType': 'snapshot',
                                    'Tags': [
                                        {
                                            'Key': 'Name',
                                            'Value': f"eks-node-backup-{instance_id}-{datetime.now().strftime('%Y%m%d')}"
                                        },
                                        {
                                            'Key': 'Environment',
                                            'Value': self.environment
                                        },
                                        {
                                            'Key': 'BackupType',
                                            'Value': 'automated'
                                        },
                                        {
                                            'Key': 'ClusterName',
                                            'Value': cluster_name
                                        }
                                    ]
                                }
                            ]
                        )
                        
                        snapshots_created += 1
                        print(f"✅ Created snapshot {snapshot['SnapshotId']} for volume {volume_id}")
            
            print(f"✅ EBS snapshot backup completed: {snapshots_created} snapshots created")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create EBS snapshots: {e}")
            print(f"❌ Failed to create EBS snapshots: {e}")
            return False
    
    def full_backup(self) -> bool:
        """
        Perform a full backup of all infrastructure components.
        
        Returns:
            bool: True if successful
        """
        print("\n🔄 Starting full infrastructure backup...")
        
        backup_bucket = f"ce-capstone-backups-{self.environment}"
        state_bucket = self.config.get("terraform", {}).get("backend", {}).get("bucket")
        cluster_name = self.config.get("eks", {}).get("cluster_name", f"ce-capstone-{self.environment}")
        
        success = True
        
        # Create backup bucket
        if not self.create_backup_bucket(backup_bucket):
            success = False
        
        # Backup Terraform state
        if state_bucket:
            if not self.backup_terraform_state(state_bucket, backup_bucket):
                success = False
        else:
            print("⚠️ No Terraform state bucket configured, skipping state backup")
        
        # Backup Kubernetes resources
        if not self.backup_kubernetes_resources(cluster_name, backup_bucket):
            success = False
        
        # Create EBS snapshots
        if not self.create_ebs_snapshots(cluster_name):
            success = False
        
        if success:
            print("✅ Full backup completed successfully!")
        else:
            print("⚠️ Backup completed with some errors")
        
        return success
    
    def list_backups(self, backup_bucket: str) -> List[Dict[str, Any]]:
        """
        List available backups.
        
        Args:
            backup_bucket: Backup bucket name
            
        Returns:
            List: Available backups
        """
        try:
            backups = []
            
            # List backup manifests
            response = self.s3.list_objects_v2(
                Bucket=backup_bucket,
                Prefix="backups/",
                Delimiter="/"
            )
            
            for obj in response.get('Contents', []):
                if obj['Key'].endswith('manifest.json'):
                    try:
                        # Get manifest content
                        manifest_obj = self.s3.get_object(
                            Bucket=backup_bucket,
                            Key=obj['Key']
                        )
                        manifest = json.loads(manifest_obj['Body'].read())
                        manifest['manifest_key'] = obj['Key']
                        backups.append(manifest)
                    except Exception as e:
                        self.logger.warning(f"Could not read manifest {obj['Key']}: {e}")
            
            return sorted(backups, key=lambda x: x.get('timestamp', ''), reverse=True)
            
        except Exception as e:
            self.logger.error(f"Failed to list backups: {e}")
            return []
    
    def restore_from_backup(self, backup_manifest: Dict[str, Any]) -> bool:
        """
        Restore infrastructure from a backup.
        
        Args:
            backup_manifest: Backup manifest information
            
        Returns:
            bool: True if successful
        """
        print(f"\n🔄 Starting restore from backup: {backup_manifest.get('timestamp')}")
        
        backup_type = backup_manifest.get('backup_type')
        
        if backup_type == 'terraform_state':
            return self._restore_terraform_state(backup_manifest)
        elif backup_type == 'kubernetes_resources':
            return self._restore_kubernetes_resources(backup_manifest)
        else:
            print(f"❌ Unknown backup type: {backup_type}")
            return False
    
    def _restore_terraform_state(self, manifest: Dict[str, Any]) -> bool:
        """Restore Terraform state from backup."""
        try:
            print("🔄 Restoring Terraform state...")
            # Implementation would restore state files from backup
            print("✅ Terraform state restored successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to restore Terraform state: {e}")
            return False
    
    def _restore_kubernetes_resources(self, manifest: Dict[str, Any]) -> bool:
        """Restore Kubernetes resources from backup."""
        try:
            print("🔄 Restoring Kubernetes resources...")
            # Implementation would restore K8s resources from backup
            print("✅ Kubernetes resources restored successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to restore Kubernetes resources: {e}")
            return False
