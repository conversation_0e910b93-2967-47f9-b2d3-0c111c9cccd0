"""
Progress Tracker Module
Provides visual progress tracking and status monitoring for deployment tasks.
"""

import os
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """Represents a deployment task with status and timing information."""
    name: str
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    message: str = ""
    subtasks: List['Task'] = field(default_factory=list)
    progress: float = 0.0  # 0.0 to 1.0
    
    @property
    def duration(self) -> Optional[timedelta]:
        """Get task duration if completed."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return datetime.now() - self.start_time
        return None
    
    @property
    def is_active(self) -> bool:
        """Check if task is currently active."""
        return self.status == TaskStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        """Check if task is completed (success or failure)."""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]


class ProgressTracker:
    """
    Tracks and displays progress of deployment tasks with real-time updates.
    Supports nested tasks and provides visual feedback.
    """
    
    def __init__(self):
        """Initialize the progress tracker."""
        self.tasks: Dict[str, Task] = {}
        self.current_task: Optional[str] = None
        self.display_thread: Optional[threading.Thread] = None
        self.stop_display = threading.Event()
        self.lock = threading.Lock()
        
        # Display settings
        self.show_progress_bar = True
        self.show_timing = True
        self.refresh_interval = 0.5  # seconds
    
    def start_task(self, task_name: str, message: str = "") -> None:
        """
        Start a new task.
        
        Args:
            task_name: Name of the task
            message: Optional initial message
        """
        with self.lock:
            task = Task(
                name=task_name,
                status=TaskStatus.RUNNING,
                start_time=datetime.now(),
                message=message
            )
            self.tasks[task_name] = task
            self.current_task = task_name
        
        print(f"\n🚀 Starting: {task_name}")
        if message:
            print(f"   {message}")
    
    def update_task(self, task_name: str, progress: float = None, message: str = None) -> None:
        """
        Update task progress and message.
        
        Args:
            task_name: Name of the task
            progress: Progress value (0.0 to 1.0)
            message: Status message
        """
        with self.lock:
            if task_name in self.tasks:
                task = self.tasks[task_name]
                if progress is not None:
                    task.progress = max(0.0, min(1.0, progress))
                if message is not None:
                    task.message = message
    
    def complete_task(self, task_name: str, message: str = "Completed successfully") -> None:
        """
        Mark a task as completed.
        
        Args:
            task_name: Name of the task
            message: Completion message
        """
        with self.lock:
            if task_name in self.tasks:
                task = self.tasks[task_name]
                task.status = TaskStatus.COMPLETED
                task.end_time = datetime.now()
                task.progress = 1.0
                task.message = message
                
                if self.current_task == task_name:
                    self.current_task = None
        
        duration = self.tasks[task_name].duration
        duration_str = f" ({duration.total_seconds():.1f}s)" if duration else ""
        print(f"✅ {task_name} completed{duration_str}")
        if message != "Completed successfully":
            print(f"   {message}")
    
    def fail_task(self, task_name: str, message: str = "Task failed") -> None:
        """
        Mark a task as failed.
        
        Args:
            task_name: Name of the task
            message: Failure message
        """
        with self.lock:
            if task_name in self.tasks:
                task = self.tasks[task_name]
                task.status = TaskStatus.FAILED
                task.end_time = datetime.now()
                task.message = message
                
                if self.current_task == task_name:
                    self.current_task = None
        
        duration = self.tasks[task_name].duration
        duration_str = f" ({duration.total_seconds():.1f}s)" if duration else ""
        print(f"❌ {task_name} failed{duration_str}")
        print(f"   {message}")
    
    def cancel_task(self, task_name: str, message: str = "Task cancelled") -> None:
        """
        Cancel a task.
        
        Args:
            task_name: Name of the task
            message: Cancellation message
        """
        with self.lock:
            if task_name in self.tasks:
                task = self.tasks[task_name]
                task.status = TaskStatus.CANCELLED
                task.end_time = datetime.now()
                task.message = message
                
                if self.current_task == task_name:
                    self.current_task = None
        
        print(f"⚠️ {task_name} cancelled")
        print(f"   {message}")
    
    def add_subtask(self, parent_task: str, subtask_name: str, message: str = "") -> None:
        """
        Add a subtask to an existing task.
        
        Args:
            parent_task: Name of the parent task
            subtask_name: Name of the subtask
            message: Optional message
        """
        with self.lock:
            if parent_task in self.tasks:
                subtask = Task(
                    name=subtask_name,
                    status=TaskStatus.RUNNING,
                    start_time=datetime.now(),
                    message=message
                )
                self.tasks[parent_task].subtasks.append(subtask)
    
    def get_progress_bar(self, progress: float, width: int = 30) -> str:
        """
        Generate a visual progress bar.
        
        Args:
            progress: Progress value (0.0 to 1.0)
            width: Width of the progress bar
            
        Returns:
            str: Progress bar string
        """
        filled = int(progress * width)
        bar = "█" * filled + "░" * (width - filled)
        percentage = int(progress * 100)
        return f"[{bar}] {percentage}%"
    
    def display_status(self) -> None:
        """Display current status of all tasks."""
        with self.lock:
            if not self.tasks:
                print("No active tasks")
                return
            
            print("\n" + "=" * 70)
            print("                    TASK STATUS")
            print("=" * 70)
            
            for task_name, task in self.tasks.items():
                # Status icon
                if task.status == TaskStatus.RUNNING:
                    icon = "🔄"
                elif task.status == TaskStatus.COMPLETED:
                    icon = "✅"
                elif task.status == TaskStatus.FAILED:
                    icon = "❌"
                elif task.status == TaskStatus.CANCELLED:
                    icon = "⚠️"
                else:
                    icon = "⏳"
                
                # Duration
                duration_str = ""
                if task.duration:
                    duration_str = f" ({task.duration.total_seconds():.1f}s)"
                
                # Progress bar for running tasks
                progress_str = ""
                if task.status == TaskStatus.RUNNING and self.show_progress_bar:
                    progress_str = f"\n     {self.get_progress_bar(task.progress)}"
                
                print(f"{icon} {task_name}{duration_str}")
                if task.message:
                    print(f"     {task.message}")
                if progress_str:
                    print(progress_str)
                
                # Show subtasks
                for subtask in task.subtasks:
                    sub_icon = "✅" if subtask.status == TaskStatus.COMPLETED else "🔄"
                    print(f"     └─ {sub_icon} {subtask.name}")
                    if subtask.message:
                        print(f"        {subtask.message}")
            
            print("=" * 70)
    
    def start_live_display(self) -> None:
        """Start live display of task progress in a separate thread."""
        if self.display_thread and self.display_thread.is_alive():
            return
        
        self.stop_display.clear()
        self.display_thread = threading.Thread(target=self._live_display_worker)
        self.display_thread.daemon = True
        self.display_thread.start()
    
    def stop_live_display(self) -> None:
        """Stop the live display thread."""
        self.stop_display.set()
        if self.display_thread:
            self.display_thread.join(timeout=1.0)
    
    def _live_display_worker(self) -> None:
        """Worker function for live display thread."""
        while not self.stop_display.is_set():
            # Clear screen and show status
            os.system('clear' if os.name == 'posix' else 'cls')
            self.display_status()
            
            # Wait for next refresh
            self.stop_display.wait(self.refresh_interval)
    
    def get_summary(self) -> Dict[str, Any]:
        """
        Get summary of all tasks.
        
        Returns:
            Dict: Summary information
        """
        with self.lock:
            total_tasks = len(self.tasks)
            completed = sum(1 for task in self.tasks.values() if task.status == TaskStatus.COMPLETED)
            failed = sum(1 for task in self.tasks.values() if task.status == TaskStatus.FAILED)
            running = sum(1 for task in self.tasks.values() if task.status == TaskStatus.RUNNING)
            
            total_duration = timedelta()
            for task in self.tasks.values():
                if task.duration:
                    total_duration += task.duration
            
            return {
                "total_tasks": total_tasks,
                "completed": completed,
                "failed": failed,
                "running": running,
                "success_rate": (completed / total_tasks * 100) if total_tasks > 0 else 0,
                "total_duration": total_duration
            }
    
    def print_summary(self) -> None:
        """Print a summary of all tasks."""
        summary = self.get_summary()
        
        print("\n" + "=" * 50)
        print("              DEPLOYMENT SUMMARY")
        print("=" * 50)
        print(f"Total Tasks: {summary['total_tasks']}")
        print(f"Completed: {summary['completed']} ✅")
        print(f"Failed: {summary['failed']} ❌")
        print(f"Running: {summary['running']} 🔄")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Total Duration: {summary['total_duration'].total_seconds():.1f}s")
        print("=" * 50)
    
    def clear_completed_tasks(self) -> None:
        """Remove completed tasks from tracking."""
        with self.lock:
            self.tasks = {
                name: task for name, task in self.tasks.items()
                if not task.is_completed
            }
    
    def reset(self) -> None:
        """Reset all tasks and stop live display."""
        self.stop_live_display()
        with self.lock:
            self.tasks.clear()
            self.current_task = None
