"""
Terraform Manager Module
Handles Terraform operations and infrastructure deployment.
"""

import os
import subprocess
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path


class TerraformManager:
    """
    Manages Terraform operations including initialization, planning, and deployment.
    Provides high-level abstractions for Terraform workflows.
    """
    
    def __init__(self, working_dir: str, config: Dict[str, Any]):
        """
        Initialize Terraform manager.
        
        Args:
            working_dir: Directory containing Terraform files
            config: Terraform configuration
        """
        self.working_dir = Path(working_dir)
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Ensure working directory exists
        self.working_dir.mkdir(parents=True, exist_ok=True)
        
        # Set environment variables
        self.env = os.environ.copy()
        self.env.update({
            "TF_IN_AUTOMATION": "true",
            "TF_INPUT": "false"
        })
        
        # Add backend configuration
        backend_config = config.get("backend", {})
        if backend_config:
            for key, value in backend_config.items():
                if key != "type":
                    self.env[f"TF_CLI_ARGS_init"] = self.env.get("TF_CLI_ARGS_init", "") + f" -backend-config='{key}={value}'"
    
    def run_command(self, command: List[str], capture_output: bool = True) -> Tuple[bool, str, str]:
        """
        Run a Terraform command.
        
        Args:
            command: Command to run
            capture_output: Whether to capture output
            
        Returns:
            Tuple of (success, stdout, stderr)
        """
        try:
            self.logger.info(f"Running command: {' '.join(command)}")
            
            result = subprocess.run(
                command,
                cwd=self.working_dir,
                env=self.env,
                capture_output=capture_output,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            success = result.returncode == 0
            stdout = result.stdout if capture_output else ""
            stderr = result.stderr if capture_output else ""
            
            if not success:
                self.logger.error(f"Command failed: {' '.join(command)}")
                self.logger.error(f"Error: {stderr}")
            
            return success, stdout, stderr
            
        except subprocess.TimeoutExpired:
            self.logger.error("Command timed out")
            return False, "", "Command timed out"
        except Exception as e:
            self.logger.error(f"Error running command: {e}")
            return False, "", str(e)
    
    def init(self) -> bool:
        """
        Initialize Terraform working directory.
        
        Returns:
            bool: True if successful
        """
        print("🔧 Initializing Terraform...")
        
        command = ["terraform", "init", "-upgrade"]
        success, stdout, stderr = self.run_command(command)
        
        if success:
            print("✅ Terraform initialized successfully")
            return True
        else:
            print(f"❌ Terraform initialization failed: {stderr}")
            return False
    
    def validate(self) -> bool:
        """
        Validate Terraform configuration.
        
        Returns:
            bool: True if valid
        """
        print("🔍 Validating Terraform configuration...")
        
        command = ["terraform", "validate"]
        success, stdout, stderr = self.run_command(command)
        
        if success:
            print("✅ Terraform configuration is valid")
            return True
        else:
            print(f"❌ Terraform validation failed: {stderr}")
            return False
    
    def plan(self, var_file: Optional[str] = None, target: Optional[str] = None) -> Tuple[bool, str]:
        """
        Create Terraform execution plan.
        
        Args:
            var_file: Path to variables file
            target: Specific resource to target
            
        Returns:
            Tuple of (success, plan_output)
        """
        print("📋 Creating Terraform plan...")
        
        command = ["terraform", "plan", "-detailed-exitcode"]
        
        if var_file:
            command.extend(["-var-file", var_file])
        
        if target:
            command.extend(["-target", target])
        
        success, stdout, stderr = self.run_command(command)
        
        # Terraform plan returns:
        # 0 = no changes
        # 1 = error
        # 2 = changes present
        if success or "2" in str(success):  # Handle exit code 2 as success with changes
            print("✅ Terraform plan created successfully")
            return True, stdout
        else:
            print(f"❌ Terraform plan failed: {stderr}")
            return False, stderr
    
    def apply(self, var_file: Optional[str] = None, target: Optional[str] = None, auto_approve: bool = True) -> bool:
        """
        Apply Terraform configuration.
        
        Args:
            var_file: Path to variables file
            target: Specific resource to target
            auto_approve: Whether to auto-approve changes
            
        Returns:
            bool: True if successful
        """
        print("🚀 Applying Terraform configuration...")
        
        command = ["terraform", "apply"]
        
        if auto_approve:
            command.append("-auto-approve")
        
        if var_file:
            command.extend(["-var-file", var_file])
        
        if target:
            command.extend(["-target", target])
        
        success, stdout, stderr = self.run_command(command, capture_output=False)
        
        if success:
            print("✅ Terraform apply completed successfully")
            return True
        else:
            print(f"❌ Terraform apply failed")
            return False
    
    def destroy(self, var_file: Optional[str] = None, target: Optional[str] = None, auto_approve: bool = True) -> bool:
        """
        Destroy Terraform-managed infrastructure.
        
        Args:
            var_file: Path to variables file
            target: Specific resource to target
            auto_approve: Whether to auto-approve destruction
            
        Returns:
            bool: True if successful
        """
        print("🧹 Destroying Terraform infrastructure...")
        
        command = ["terraform", "destroy"]
        
        if auto_approve:
            command.append("-auto-approve")
        
        if var_file:
            command.extend(["-var-file", var_file])
        
        if target:
            command.extend(["-target", target])
        
        success, stdout, stderr = self.run_command(command, capture_output=False)
        
        if success:
            print("✅ Terraform destroy completed successfully")
            return True
        else:
            print(f"❌ Terraform destroy failed")
            return False
    
    def output(self, output_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get Terraform outputs.
        
        Args:
            output_name: Specific output to retrieve
            
        Returns:
            Dict: Output values
        """
        command = ["terraform", "output", "-json"]
        
        if output_name:
            command.append(output_name)
        
        success, stdout, stderr = self.run_command(command)
        
        if success:
            try:
                return json.loads(stdout)
            except json.JSONDecodeError:
                self.logger.error("Failed to parse Terraform output JSON")
                return {}
        else:
            self.logger.error(f"Failed to get Terraform output: {stderr}")
            return {}
    
    def state_list(self) -> List[str]:
        """
        List resources in Terraform state.
        
        Returns:
            List: Resource addresses
        """
        command = ["terraform", "state", "list"]
        success, stdout, stderr = self.run_command(command)
        
        if success:
            return [line.strip() for line in stdout.split('\n') if line.strip()]
        else:
            self.logger.error(f"Failed to list Terraform state: {stderr}")
            return []
    
    def workspace_list(self) -> List[str]:
        """
        List Terraform workspaces.
        
        Returns:
            List: Workspace names
        """
        command = ["terraform", "workspace", "list"]
        success, stdout, stderr = self.run_command(command)
        
        if success:
            workspaces = []
            for line in stdout.split('\n'):
                line = line.strip()
                if line:
                    # Remove asterisk from current workspace
                    workspace = line.replace('*', '').strip()
                    if workspace:
                        workspaces.append(workspace)
            return workspaces
        else:
            self.logger.error(f"Failed to list workspaces: {stderr}")
            return []
    
    def workspace_select(self, workspace: str) -> bool:
        """
        Select Terraform workspace.
        
        Args:
            workspace: Workspace name
            
        Returns:
            bool: True if successful
        """
        # Check if workspace exists
        workspaces = self.workspace_list()
        
        if workspace not in workspaces:
            # Create workspace
            command = ["terraform", "workspace", "new", workspace]
            success, stdout, stderr = self.run_command(command)
            
            if not success:
                self.logger.error(f"Failed to create workspace {workspace}: {stderr}")
                return False
        
        # Select workspace
        command = ["terraform", "workspace", "select", workspace]
        success, stdout, stderr = self.run_command(command)
        
        if success:
            print(f"✅ Selected Terraform workspace: {workspace}")
            return True
        else:
            print(f"❌ Failed to select workspace {workspace}: {stderr}")
            return False
    
    def create_backend_config(self, component: str, backend_config: Dict[str, Any]) -> str:
        """
        Create backend configuration file for Terraform.

        Args:
            component: Component name (vpc, eks, etc.)
            backend_config: Backend configuration

        Returns:
            str: Path to backend config file
        """
        backend_file = self.working_dir / "backend.hcl"

        with open(backend_file, 'w') as f:
            f.write(f"# Backend configuration for {component}\n")
            for key, value in backend_config.items():
                if isinstance(value, str):
                    f.write(f'{key} = "{value}"\n')
                else:
                    f.write(f'{key} = {str(value).lower()}\n')

        return str(backend_file)

    def create_tfvars_file(self, variables: Dict[str, Any]) -> str:
        """
        Create terraform.tfvars file from variables.

        Args:
            variables: Variables dictionary

        Returns:
            str: Path to tfvars file
        """
        tfvars_file = self.working_dir / "terraform.tfvars"

        with open(tfvars_file, 'w') as f:
            f.write("# Auto-generated Terraform variables\n")
            f.write("# Generated by Capstone Deployer\n\n")

            for key, value in variables.items():
                if isinstance(value, str):
                    f.write(f'{key} = "{value}"\n')
                elif isinstance(value, bool):
                    f.write(f'{key} = {str(value).lower()}\n')
                elif isinstance(value, list):
                    if all(isinstance(item, str) for item in value):
                        formatted_list = '["' + '", "'.join(value) + '"]'
                    else:
                        formatted_list = str(value)
                    f.write(f'{key} = {formatted_list}\n')
                elif isinstance(value, dict):
                    f.write(f'{key} = {{\n')
                    for k, v in value.items():
                        if isinstance(v, str):
                            f.write(f'  {k} = "{v}"\n')
                        else:
                            f.write(f'  {k} = {v}\n')
                    f.write('}\n')
                else:
                    f.write(f'{key} = {value}\n')

        return str(tfvars_file)

    def deploy(self, component: str, variables: Optional[Dict[str, Any]] = None,
               backend_config: Optional[Dict[str, Any]] = None) -> bool:
        """
        Deploy a specific component using Terraform.

        Args:
            component: Component name (vpc, eks, etc.)
            variables: Variables to pass to Terraform
            backend_config: Backend configuration

        Returns:
            bool: True if successful
        """
        print(f"🚀 Deploying {component} infrastructure...")

        # Create backend config if provided
        backend_file = None
        if backend_config:
            backend_file = self.create_backend_config(component, backend_config)

        # Create tfvars file if variables provided
        tfvars_file = None
        if variables:
            tfvars_file = self.create_tfvars_file(variables)

        # Initialize with backend config
        init_command = ["terraform", "init", "-upgrade"]
        if backend_file:
            init_command.extend(["-backend-config", backend_file])

        success, stdout, stderr = self.run_command(init_command)
        if not success:
            print(f"❌ Terraform initialization failed: {stderr}")
            return False

        print("✅ Terraform initialized successfully")

        # Select workspace if configured
        workspace = self.config.get("workspace")
        if workspace:
            if not self.workspace_select(workspace):
                return False

        # Validate
        if not self.validate():
            return False

        # Plan
        plan_success, plan_output = self.plan(tfvars_file)
        if not plan_success:
            return False

        # Apply
        return self.apply(tfvars_file)
    
    def destroy_all(self, var_file: Optional[str] = None) -> bool:
        """
        Destroy all Terraform-managed infrastructure.
        
        Args:
            var_file: Path to variables file
            
        Returns:
            bool: True if successful
        """
        print("⚠️ Destroying ALL Terraform infrastructure...")
        
        # Confirm destruction
        confirm = input("Type 'DESTROY' to confirm: ").strip()
        if confirm != 'DESTROY':
            print("❌ Destruction cancelled")
            return False
        
        # Select workspace if configured
        workspace = self.config.get("workspace")
        if workspace:
            if not self.workspace_select(workspace):
                return False
        
        # Initialize (in case state is not initialized)
        self.init()
        
        # Destroy
        return self.destroy(var_file)
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get Terraform deployment status.
        
        Returns:
            Dict: Status information
        """
        status = {
            "initialized": False,
            "workspace": None,
            "resources": [],
            "outputs": {}
        }
        
        # Check if initialized
        if (self.working_dir / ".terraform").exists():
            status["initialized"] = True
        
        # Get current workspace
        command = ["terraform", "workspace", "show"]
        success, stdout, stderr = self.run_command(command)
        if success:
            status["workspace"] = stdout.strip()
        
        # Get resources
        if status["initialized"]:
            status["resources"] = self.state_list()
            status["outputs"] = self.output()
        
        return status
