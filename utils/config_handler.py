"""
Configuration Handler Module
Manages configuration files and environment-specific settings.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path


class ConfigHandler:
    """
    Handles configuration management for different environments.
    Supports YAML configuration files with environment-specific overrides.
    """
    
    def __init__(self, config_path: str):
        """
        Initialize configuration handler.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        self.config: Dict[str, Any] = {}
        
        # Create config directory if it doesn't exist
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load or create configuration
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file or create default if not exists."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    self.config = yaml.safe_load(f) or {}
                self.logger.info(f"Configuration loaded from {self.config_path}")
            else:
                self.config = self.get_default_config()
                self.save_config()
                self.logger.info(f"Default configuration created at {self.config_path}")
                
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            self.config = self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration structure.
        
        Returns:
            Dict: Default configuration
        """
        environment = self.config_path.stem  # Extract environment from filename
        
        return {
            "environment": environment,
            "aws": {
                "region": "us-west-2",
                "profile": "default",
                "tags": {
                    "Project": "ce-capstone-grp1",
                    "Environment": environment,
                    "ManagedBy": "capstone-deployer"
                }
            },
            "vpc": {
                "cidr_block": "10.0.0.0/16",
                "availability_zones": ["us-west-2a", "us-west-2b", "us-west-2c"],
                "public_subnets": ["********/24", "********/24", "********/24"],
                "private_subnets": ["*********/24", "*********/24", "*********/24"],
                "enable_nat_gateway": True,
                "enable_vpn_gateway": False,
                "enable_dns_hostnames": True,
                "enable_dns_support": True
            },
            "eks": {
                "cluster_name": f"ce-capstone-{environment}",
                "cluster_version": "1.28",
                "node_groups": {
                    "main": {
                        "instance_types": ["t3.medium"],
                        "min_size": 1,
                        "max_size": 3,
                        "desired_size": 2,
                        "disk_size": 20,
                        "ami_type": "AL2_x86_64"
                    }
                },
                "addons": [
                    "vpc-cni",
                    "coredns",
                    "kube-proxy",
                    "aws-ebs-csi-driver"
                ]
            },
            "applications": {
                "namespace": f"capstone-{environment}",
                "apps": [
                    {
                        "name": "frontend",
                        "image": "nginx:latest",
                        "replicas": 2,
                        "port": 80,
                        "service_type": "LoadBalancer"
                    },
                    {
                        "name": "backend",
                        "image": "node:18-alpine",
                        "replicas": 2,
                        "port": 3000,
                        "service_type": "ClusterIP"
                    }
                ]
            },
            "terraform": {
                "backend": {
                    "type": "s3",
                    "bucket": f"ce-capstone-terraform-state-{environment}",
                    "key": "terraform.tfstate",
                    "region": "us-west-2",
                    "encrypt": True,
                    "dynamodb_table": f"ce-capstone-terraform-locks-{environment}"
                },
                "workspace": environment
            },
            "monitoring": {
                "enable_cloudwatch": True,
                "enable_prometheus": True,
                "log_retention_days": 7 if environment == "dev" else 30
            },
            "security": {
                "enable_pod_security_policy": True,
                "enable_network_policy": True,
                "allowed_cidr_blocks": ["0.0.0.0/0"]  # Restrict in production
            }
        }
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            self.logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            raise
    
    def get_config(self, key_path: str = None) -> Any:
        """
        Get configuration value by key path.
        
        Args:
            key_path: Dot-separated path to configuration key (e.g., 'aws.region')
            
        Returns:
            Configuration value or entire config if key_path is None
        """
        if key_path is None:
            return self.config
        
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            self.logger.warning(f"Configuration key not found: {key_path}")
            return None
    
    def set_config(self, key_path: str, value: Any) -> None:
        """
        Set configuration value by key path.
        
        Args:
            key_path: Dot-separated path to configuration key
            value: Value to set
        """
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to parent of target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        self.save_config()
    
    def get_aws_config(self) -> Dict[str, Any]:
        """Get AWS-specific configuration."""
        return self.get_config('aws') or {}
    
    def get_vpc_config(self) -> Dict[str, Any]:
        """Get VPC-specific configuration."""
        return self.get_config('vpc') or {}
    
    def get_eks_config(self) -> Dict[str, Any]:
        """Get EKS-specific configuration."""
        return self.get_config('eks') or {}
    
    def get_terraform_config(self) -> Dict[str, Any]:
        """Get Terraform-specific configuration."""
        return self.get_config('terraform') or {}
    
    def get_k8s_config(self) -> Dict[str, Any]:
        """Get Kubernetes-specific configuration."""
        return self.get_config('applications') or {}
    
    def get_environment_variables(self) -> Dict[str, str]:
        """
        Get environment variables for deployment.
        
        Returns:
            Dict of environment variables
        """
        aws_config = self.get_aws_config()
        
        env_vars = {
            "AWS_DEFAULT_REGION": aws_config.get("region", "us-west-2"),
            "AWS_PROFILE": aws_config.get("profile", "default"),
            "TF_VAR_environment": self.config.get("environment", "dev"),
            "TF_VAR_project_name": "ce-capstone-grp1"
        }
        
        # Add VPC variables
        vpc_config = self.get_vpc_config()
        if vpc_config:
            env_vars.update({
                "TF_VAR_vpc_cidr": vpc_config.get("cidr_block", "10.0.0.0/16"),
                "TF_VAR_availability_zones": ",".join(vpc_config.get("availability_zones", [])),
                "TF_VAR_public_subnets": ",".join(vpc_config.get("public_subnets", [])),
                "TF_VAR_private_subnets": ",".join(vpc_config.get("private_subnets", []))
            })
        
        # Add EKS variables
        eks_config = self.get_eks_config()
        if eks_config:
            env_vars.update({
                "TF_VAR_cluster_name": eks_config.get("cluster_name", "ce-capstone-dev"),
                "TF_VAR_cluster_version": eks_config.get("cluster_version", "1.28")
            })
        
        return env_vars
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """
        Validate configuration for required fields.
        
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Required top-level keys
        required_keys = ["environment", "aws", "vpc", "eks", "applications"]
        for key in required_keys:
            if key not in self.config:
                errors.append(f"Missing required configuration section: {key}")
        
        # Validate AWS config
        aws_config = self.get_aws_config()
        if aws_config:
            if not aws_config.get("region"):
                errors.append("AWS region is required")
        
        # Validate VPC config
        vpc_config = self.get_vpc_config()
        if vpc_config:
            if not vpc_config.get("cidr_block"):
                errors.append("VPC CIDR block is required")
            if not vpc_config.get("availability_zones"):
                errors.append("VPC availability zones are required")
        
        # Validate EKS config
        eks_config = self.get_eks_config()
        if eks_config:
            if not eks_config.get("cluster_name"):
                errors.append("EKS cluster name is required")
        
        return len(errors) == 0, errors
    
    def display_config(self) -> None:
        """Display current configuration in a readable format."""
        print("\n" + "=" * 60)
        print("           CURRENT CONFIGURATION")
        print("=" * 60)
        
        def print_dict(d: Dict, indent: int = 0):
            """Recursively print dictionary with indentation."""
            for key, value in d.items():
                if isinstance(value, dict):
                    print("  " * indent + f"{key}:")
                    print_dict(value, indent + 1)
                elif isinstance(value, list):
                    print("  " * indent + f"{key}: {', '.join(map(str, value))}")
                else:
                    print("  " * indent + f"{key}: {value}")
        
        print_dict(self.config)
        print("=" * 60)
    
    def edit_config(self) -> None:
        """Interactive configuration editor."""
        print("\n⚙️ Configuration Editor")
        print("Enter key path and new value (e.g., 'aws.region us-east-1')")
        print("Type 'done' to finish, 'show' to display config")
        
        while True:
            try:
                user_input = input("\nconfig> ").strip()
                
                if user_input.lower() == 'done':
                    break
                elif user_input.lower() == 'show':
                    self.display_config()
                    continue
                
                parts = user_input.split(' ', 1)
                if len(parts) != 2:
                    print("❌ Invalid format. Use: key.path value")
                    continue
                
                key_path, value = parts
                
                # Try to convert value to appropriate type
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                elif value.isdigit():
                    value = int(value)
                elif ',' in value:
                    value = [item.strip() for item in value.split(',')]
                
                self.set_config(key_path, value)
                print(f"✅ Set {key_path} = {value}")
                
            except KeyboardInterrupt:
                print("\n❌ Configuration editing cancelled")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def create_terraform_vars_file(self, output_path: str) -> None:
        """
        Create Terraform variables file from configuration.
        
        Args:
            output_path: Path to output terraform.tfvars file
        """
        try:
            env_vars = self.get_environment_variables()
            
            # Convert to Terraform format
            tf_vars = []
            for key, value in env_vars.items():
                if key.startswith("TF_VAR_"):
                    var_name = key[7:]  # Remove TF_VAR_ prefix
                    if isinstance(value, str) and ',' in value:
                        # Convert comma-separated string to list
                        values_list = [f'"{v.strip()}"' for v in value.split(",")]
                        tf_vars.append(f'{var_name} = [{", ".join(values_list)}]')
                    else:
                        tf_vars.append(f'{var_name} = "{value}"')
            
            with open(output_path, 'w') as f:
                f.write("# Auto-generated Terraform variables\n")
                f.write("# Generated by Capstone Deployer\n\n")
                f.write("\n".join(tf_vars))
            
            self.logger.info(f"Terraform variables file created: {output_path}")
            
        except Exception as e:
            self.logger.error(f"Error creating Terraform vars file: {e}")
            raise
