"""
Security Manager Mo<PERSON><PERSON>
Handles security scanning, compliance checks, and security best practices.
"""

import boto3
import json
import logging
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


class SecurityManager:
    """
    Manages security aspects of the infrastructure deployment.
    Performs security scans, compliance checks, and implements security best practices.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize security manager.
        
        Args:
            config: Security configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.region = config.get("aws", {}).get("region", "us-west-2")
        self.environment = config.get("environment", "dev")
        
        # Initialize AWS clients
        try:
            self.iam = boto3.client('iam')
            self.ec2 = boto3.client('ec2', region_name=self.region)
            self.eks = boto3.client('eks', region_name=self.region)
            self.securityhub = boto3.client('securityhub', region_name=self.region)
            self.inspector = boto3.client('inspector2', region_name=self.region)
        except Exception as e:
            self.logger.error(f"Failed to initialize AWS clients: {e}")
            raise
    
    def scan_security_groups(self) -> Dict[str, Any]:
        """
        Scan security groups for potential security issues.
        
        Returns:
            Dict: Security group scan results
        """
        try:
            print("🔍 Scanning security groups...")
            
            security_groups = self.ec2.describe_security_groups()
            issues = []
            compliant_groups = 0
            
            for sg in security_groups['SecurityGroups']:
                sg_issues = []
                
                # Check for overly permissive inbound rules
                for rule in sg.get('IpPermissions', []):
                    for ip_range in rule.get('IpRanges', []):
                        if ip_range.get('CidrIp') == '0.0.0.0/0':
                            if rule.get('FromPort') == 22 or rule.get('FromPort') == 3389:
                                sg_issues.append({
                                    "severity": "HIGH",
                                    "issue": f"SSH/RDP port {rule.get('FromPort')} open to 0.0.0.0/0",
                                    "rule": rule
                                })
                            elif rule.get('FromPort') != 80 and rule.get('FromPort') != 443:
                                sg_issues.append({
                                    "severity": "MEDIUM",
                                    "issue": f"Port {rule.get('FromPort')} open to 0.0.0.0/0",
                                    "rule": rule
                                })
                
                # Check for unused security groups
                if not sg.get('IpPermissions') and not sg.get('IpPermissionsEgress'):
                    sg_issues.append({
                        "severity": "LOW",
                        "issue": "Security group has no rules defined",
                        "rule": None
                    })
                
                if sg_issues:
                    issues.append({
                        "group_id": sg['GroupId'],
                        "group_name": sg.get('GroupName', 'N/A'),
                        "vpc_id": sg.get('VpcId', 'N/A'),
                        "issues": sg_issues
                    })
                else:
                    compliant_groups += 1
            
            result = {
                "scan_type": "security_groups",
                "timestamp": datetime.now().isoformat(),
                "total_groups": len(security_groups['SecurityGroups']),
                "compliant_groups": compliant_groups,
                "groups_with_issues": len(issues),
                "issues": issues
            }
            
            print(f"✅ Security group scan completed: {len(issues)} groups with issues")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to scan security groups: {e}")
            return {"error": str(e)}
    
    def scan_iam_policies(self) -> Dict[str, Any]:
        """
        Scan IAM policies for overly permissive permissions.
        
        Returns:
            Dict: IAM policy scan results
        """
        try:
            print("🔍 Scanning IAM policies...")
            
            issues = []
            
            # Get all roles
            roles = self.iam.list_roles()
            
            for role in roles['Roles']:
                role_name = role['RoleName']
                
                # Skip AWS service roles
                if role_name.startswith('aws-service-role'):
                    continue
                
                # Get attached policies
                attached_policies = self.iam.list_attached_role_policies(RoleName=role_name)
                
                for policy in attached_policies['AttachedPolicies']:
                    policy_arn = policy['PolicyArn']
                    
                    # Skip AWS managed policies for now
                    if policy_arn.startswith('arn:aws:iam::aws:'):
                        continue
                    
                    try:
                        # Get policy document
                        policy_version = self.iam.get_policy(PolicyArn=policy_arn)
                        policy_doc = self.iam.get_policy_version(
                            PolicyArn=policy_arn,
                            VersionId=policy_version['Policy']['DefaultVersionId']
                        )
                        
                        document = policy_doc['PolicyVersion']['Document']
                        
                        # Check for overly permissive statements
                        for statement in document.get('Statement', []):
                            if statement.get('Effect') == 'Allow':
                                actions = statement.get('Action', [])
                                resources = statement.get('Resource', [])
                                
                                # Check for wildcard actions
                                if '*' in actions or (isinstance(actions, str) and actions == '*'):
                                    issues.append({
                                        "severity": "HIGH",
                                        "role": role_name,
                                        "policy": policy['PolicyName'],
                                        "issue": "Policy allows all actions (*)",
                                        "statement": statement
                                    })
                                
                                # Check for wildcard resources
                                if '*' in resources or (isinstance(resources, str) and resources == '*'):
                                    issues.append({
                                        "severity": "MEDIUM",
                                        "role": role_name,
                                        "policy": policy['PolicyName'],
                                        "issue": "Policy allows access to all resources (*)",
                                        "statement": statement
                                    })
                    
                    except Exception as e:
                        self.logger.warning(f"Could not analyze policy {policy_arn}: {e}")
            
            result = {
                "scan_type": "iam_policies",
                "timestamp": datetime.now().isoformat(),
                "total_roles_scanned": len(roles['Roles']),
                "issues_found": len(issues),
                "issues": issues
            }
            
            print(f"✅ IAM policy scan completed: {len(issues)} issues found")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to scan IAM policies: {e}")
            return {"error": str(e)}
    
    def scan_kubernetes_security(self, cluster_name: str) -> Dict[str, Any]:
        """
        Scan Kubernetes cluster for security issues.
        
        Args:
            cluster_name: Name of the EKS cluster
            
        Returns:
            Dict: Kubernetes security scan results
        """
        try:
            print("🔍 Scanning Kubernetes security...")
            
            # Update kubeconfig
            subprocess.run([
                "aws", "eks", "update-kubeconfig",
                "--region", self.region,
                "--name", cluster_name
            ], check=True, capture_output=True)
            
            issues = []
            
            # Check for pods running as root
            try:
                result = subprocess.run([
                    "kubectl", "get", "pods", "--all-namespaces",
                    "-o", "jsonpath={range .items[*]}{.metadata.namespace}{' '}{.metadata.name}{' '}{.spec.securityContext.runAsUser}{'\n'}{end}"
                ], capture_output=True, text=True, check=True)
                
                for line in result.stdout.strip().split('\n'):
                    if line:
                        parts = line.split()
                        if len(parts) >= 3 and (parts[2] == '0' or parts[2] == '<no value>'):
                            issues.append({
                                "severity": "MEDIUM",
                                "namespace": parts[0],
                                "pod": parts[1],
                                "issue": "Pod running as root user",
                                "type": "pod_security"
                            })
            except subprocess.CalledProcessError:
                pass
            
            # Check for privileged containers
            try:
                result = subprocess.run([
                    "kubectl", "get", "pods", "--all-namespaces",
                    "-o", "json"
                ], capture_output=True, text=True, check=True)
                
                pods_data = json.loads(result.stdout)
                
                for pod in pods_data.get('items', []):
                    namespace = pod['metadata']['namespace']
                    pod_name = pod['metadata']['name']
                    
                    for container in pod['spec'].get('containers', []):
                        security_context = container.get('securityContext', {})
                        if security_context.get('privileged'):
                            issues.append({
                                "severity": "HIGH",
                                "namespace": namespace,
                                "pod": pod_name,
                                "container": container['name'],
                                "issue": "Container running in privileged mode",
                                "type": "container_security"
                            })
            except (subprocess.CalledProcessError, json.JSONDecodeError):
                pass
            
            # Check for missing network policies
            try:
                result = subprocess.run([
                    "kubectl", "get", "networkpolicies", "--all-namespaces"
                ], capture_output=True, text=True, check=True)
                
                if "No resources found" in result.stdout:
                    issues.append({
                        "severity": "MEDIUM",
                        "issue": "No network policies found - network traffic not restricted",
                        "type": "network_security"
                    })
            except subprocess.CalledProcessError:
                pass
            
            result = {
                "scan_type": "kubernetes_security",
                "timestamp": datetime.now().isoformat(),
                "cluster_name": cluster_name,
                "issues_found": len(issues),
                "issues": issues
            }
            
            print(f"✅ Kubernetes security scan completed: {len(issues)} issues found")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to scan Kubernetes security: {e}")
            return {"error": str(e)}
    
    def run_comprehensive_security_scan(self) -> Dict[str, Any]:
        """
        Run a comprehensive security scan of the entire infrastructure.
        
        Returns:
            Dict: Comprehensive security scan results
        """
        print("\n🔒 Starting comprehensive security scan...")
        
        scan_results = {
            "scan_timestamp": datetime.now().isoformat(),
            "environment": self.environment,
            "scans": {},
            "summary": {
                "total_issues": 0,
                "high_severity": 0,
                "medium_severity": 0,
                "low_severity": 0
            }
        }
        
        # Security group scan
        sg_results = self.scan_security_groups()
        scan_results["scans"]["security_groups"] = sg_results
        
        # IAM policy scan
        iam_results = self.scan_iam_policies()
        scan_results["scans"]["iam_policies"] = iam_results
        
        # Kubernetes security scan
        cluster_name = self.config.get("eks", {}).get("cluster_name", f"ce-capstone-{self.environment}")
        k8s_results = self.scan_kubernetes_security(cluster_name)
        scan_results["scans"]["kubernetes_security"] = k8s_results
        
        # Calculate summary
        for scan_name, scan_data in scan_results["scans"].items():
            if "issues" in scan_data:
                for issue_group in scan_data["issues"]:
                    if isinstance(issue_group, dict):
                        if "issues" in issue_group:  # Security groups format
                            for issue in issue_group["issues"]:
                                scan_results["summary"]["total_issues"] += 1
                                severity = issue.get("severity", "LOW")
                                scan_results["summary"][f"{severity.lower()}_severity"] += 1
                        else:  # Direct issue format
                            scan_results["summary"]["total_issues"] += 1
                            severity = issue_group.get("severity", "LOW")
                            scan_results["summary"][f"{severity.lower()}_severity"] += 1
        
        return scan_results
    
    def display_security_report(self, scan_results: Dict[str, Any]) -> None:
        """
        Display security scan results in a readable format.
        
        Args:
            scan_results: Security scan results
        """
        print("\n" + "=" * 60)
        print("           SECURITY SCAN REPORT")
        print("=" * 60)
        
        summary = scan_results.get("summary", {})
        print(f"\n📊 Summary:")
        print(f"   Total Issues: {summary.get('total_issues', 0)}")
        print(f"   🔴 High Severity: {summary.get('high_severity', 0)}")
        print(f"   🟡 Medium Severity: {summary.get('medium_severity', 0)}")
        print(f"   🟢 Low Severity: {summary.get('low_severity', 0)}")
        
        print(f"\n📅 Scan Date: {scan_results.get('scan_timestamp', 'Unknown')}")
        print(f"🌍 Environment: {scan_results.get('environment', 'Unknown')}")
        
        # Display issues by category
        for scan_name, scan_data in scan_results.get("scans", {}).items():
            print(f"\n🔍 {scan_name.replace('_', ' ').title()}:")
            
            if "error" in scan_data:
                print(f"   ❌ Scan failed: {scan_data['error']}")
                continue
            
            issues = scan_data.get("issues", [])
            if not issues:
                print("   ✅ No issues found")
                continue
            
            for i, issue in enumerate(issues[:5], 1):  # Show first 5 issues
                if isinstance(issue, dict):
                    if "issues" in issue:  # Security groups format
                        print(f"   {i}. Group: {issue.get('group_name', 'Unknown')}")
                        for sub_issue in issue["issues"][:3]:  # Show first 3 sub-issues
                            severity_icon = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}.get(sub_issue.get("severity"), "⚪")
                            print(f"      {severity_icon} {sub_issue.get('issue', 'Unknown issue')}")
                    else:  # Direct issue format
                        severity_icon = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}.get(issue.get("severity"), "⚪")
                        print(f"   {i}. {severity_icon} {issue.get('issue', 'Unknown issue')}")
            
            if len(issues) > 5:
                print(f"   ... and {len(issues) - 5} more issues")
        
        print("\n💡 Recommendations:")
        if summary.get("high_severity", 0) > 0:
            print("   🔴 Address high severity issues immediately")
        if summary.get("medium_severity", 0) > 0:
            print("   🟡 Review and fix medium severity issues")
        print("   📋 Run regular security scans")
        print("   🔒 Implement least privilege access")
        print("   🛡️ Enable AWS Security Hub for continuous monitoring")
        
        print("=" * 60)
    
    def generate_security_recommendations(self, scan_results: Dict[str, Any]) -> List[str]:
        """
        Generate security recommendations based on scan results.
        
        Args:
            scan_results: Security scan results
            
        Returns:
            List: Security recommendations
        """
        recommendations = []
        
        summary = scan_results.get("summary", {})
        
        if summary.get("high_severity", 0) > 0:
            recommendations.append("🔴 URGENT: Address high severity security issues immediately")
        
        # Security group recommendations
        sg_scan = scan_results.get("scans", {}).get("security_groups", {})
        if sg_scan.get("groups_with_issues", 0) > 0:
            recommendations.extend([
                "🔒 Review security group rules and remove overly permissive access",
                "🌐 Restrict SSH/RDP access to specific IP ranges",
                "🚪 Use bastion hosts for administrative access"
            ])
        
        # IAM recommendations
        iam_scan = scan_results.get("scans", {}).get("iam_policies", {})
        if iam_scan.get("issues_found", 0) > 0:
            recommendations.extend([
                "👤 Implement least privilege IAM policies",
                "🔑 Remove wildcard permissions where possible",
                "📋 Regular IAM access reviews"
            ])
        
        # Kubernetes recommendations
        k8s_scan = scan_results.get("scans", {}).get("kubernetes_security", {})
        if k8s_scan.get("issues_found", 0) > 0:
            recommendations.extend([
                "🐳 Implement Pod Security Standards",
                "🚫 Avoid running containers as root",
                "🌐 Implement Kubernetes Network Policies",
                "🔒 Use security contexts for containers"
            ])
        
        # General recommendations
        recommendations.extend([
            "📊 Enable AWS Security Hub for continuous monitoring",
            "🔍 Set up automated security scanning in CI/CD",
            "📝 Document security procedures and incident response",
            "🎓 Provide security training for development team"
        ])
        
        return recommendations
