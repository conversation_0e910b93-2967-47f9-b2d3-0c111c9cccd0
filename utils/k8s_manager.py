"""
Kubernetes Manager <PERSON><PERSON><PERSON>
Handles Kubernetes operations and application deployment.
"""

import os
import subprocess
import yaml
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path


class K8sManager:
    """
    Manages Kubernetes operations including application deployment and monitoring.
    Provides high-level abstractions for common Kubernetes tasks.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Kubernetes manager.
        
        Args:
            config: Kubernetes configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.namespace = config.get("namespace", "default")
        self.manifests_dir = Path("infrastructure/kubernetes")
        
        # Ensure manifests directory exists
        self.manifests_dir.mkdir(parents=True, exist_ok=True)
        
        # Set environment variables
        self.env = os.environ.copy()
    
    def run_kubectl(self, command: List[str], capture_output: bool = True) -> Tuple[bool, str, str]:
        """
        Run a kubectl command.
        
        Args:
            command: kubectl command to run
            capture_output: Whether to capture output
            
        Returns:
            Tuple of (success, stdout, stderr)
        """
        try:
            full_command = ["kubectl"] + command
            self.logger.info(f"Running kubectl: {' '.join(full_command)}")
            
            result = subprocess.run(
                full_command,
                env=self.env,
                capture_output=capture_output,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            success = result.returncode == 0
            stdout = result.stdout if capture_output else ""
            stderr = result.stderr if capture_output else ""
            
            if not success:
                self.logger.error(f"kubectl command failed: {' '.join(full_command)}")
                self.logger.error(f"Error: {stderr}")
            
            return success, stdout, stderr
            
        except subprocess.TimeoutExpired:
            self.logger.error("kubectl command timed out")
            return False, "", "Command timed out"
        except Exception as e:
            self.logger.error(f"Error running kubectl: {e}")
            return False, "", str(e)
    
    def check_cluster_connection(self) -> bool:
        """
        Check if kubectl can connect to the cluster.
        
        Returns:
            bool: True if connected
        """
        print("🔍 Checking Kubernetes cluster connection...")
        
        success, stdout, stderr = self.run_kubectl(["cluster-info"])
        
        if success:
            print("✅ Connected to Kubernetes cluster")
            return True
        else:
            print(f"❌ Cannot connect to Kubernetes cluster: {stderr}")
            return False
    
    def create_namespace(self, namespace: str = None) -> bool:
        """
        Create namespace if it doesn't exist.
        
        Args:
            namespace: Namespace name (uses default if None)
            
        Returns:
            bool: True if successful
        """
        ns = namespace or self.namespace
        
        # Check if namespace exists
        success, stdout, stderr = self.run_kubectl(["get", "namespace", ns])
        
        if success:
            print(f"✅ Namespace {ns} already exists")
            return True
        
        # Create namespace
        print(f"🔧 Creating namespace: {ns}")
        success, stdout, stderr = self.run_kubectl(["create", "namespace", ns])
        
        if success:
            print(f"✅ Namespace {ns} created successfully")
            return True
        else:
            print(f"❌ Failed to create namespace {ns}: {stderr}")
            return False
    
    def apply_manifest(self, manifest_path: str, namespace: str = None) -> bool:
        """
        Apply a Kubernetes manifest.
        
        Args:
            manifest_path: Path to manifest file
            namespace: Namespace to apply to
            
        Returns:
            bool: True if successful
        """
        ns = namespace or self.namespace
        
        command = ["apply", "-f", manifest_path]
        if ns != "default":
            command.extend(["-n", ns])
        
        success, stdout, stderr = self.run_kubectl(command, capture_output=False)
        
        if success:
            print(f"✅ Applied manifest: {manifest_path}")
            return True
        else:
            print(f"❌ Failed to apply manifest {manifest_path}")
            return False
    
    def delete_manifest(self, manifest_path: str, namespace: str = None) -> bool:
        """
        Delete resources from a Kubernetes manifest.
        
        Args:
            manifest_path: Path to manifest file
            namespace: Namespace to delete from
            
        Returns:
            bool: True if successful
        """
        ns = namespace or self.namespace
        
        command = ["delete", "-f", manifest_path]
        if ns != "default":
            command.extend(["-n", ns])
        
        success, stdout, stderr = self.run_kubectl(command)
        
        if success:
            print(f"✅ Deleted resources from: {manifest_path}")
            return True
        else:
            print(f"❌ Failed to delete resources from {manifest_path}: {stderr}")
            return False
    
    def get_pods(self, namespace: str = None) -> List[Dict[str, Any]]:
        """
        Get pods in namespace.
        
        Args:
            namespace: Namespace to query
            
        Returns:
            List: Pod information
        """
        ns = namespace or self.namespace
        
        command = ["get", "pods", "-o", "json"]
        if ns != "default":
            command.extend(["-n", ns])
        
        success, stdout, stderr = self.run_kubectl(command)
        
        if success:
            try:
                data = json.loads(stdout)
                return data.get("items", [])
            except json.JSONDecodeError:
                self.logger.error("Failed to parse pods JSON")
                return []
        else:
            self.logger.error(f"Failed to get pods: {stderr}")
            return []
    
    def get_services(self, namespace: str = None) -> List[Dict[str, Any]]:
        """
        Get services in namespace.
        
        Args:
            namespace: Namespace to query
            
        Returns:
            List: Service information
        """
        ns = namespace or self.namespace
        
        command = ["get", "services", "-o", "json"]
        if ns != "default":
            command.extend(["-n", ns])
        
        success, stdout, stderr = self.run_kubectl(command)
        
        if success:
            try:
                data = json.loads(stdout)
                return data.get("items", [])
            except json.JSONDecodeError:
                self.logger.error("Failed to parse services JSON")
                return []
        else:
            self.logger.error(f"Failed to get services: {stderr}")
            return []
    
    def wait_for_deployment(self, deployment_name: str, namespace: str = None, timeout: int = 300) -> bool:
        """
        Wait for deployment to be ready.
        
        Args:
            deployment_name: Name of deployment
            namespace: Namespace
            timeout: Timeout in seconds
            
        Returns:
            bool: True if ready
        """
        ns = namespace or self.namespace
        
        print(f"⏳ Waiting for deployment {deployment_name} to be ready...")
        
        command = ["wait", "--for=condition=available", f"deployment/{deployment_name}", f"--timeout={timeout}s"]
        if ns != "default":
            command.extend(["-n", ns])
        
        success, stdout, stderr = self.run_kubectl(command)
        
        if success:
            print(f"✅ Deployment {deployment_name} is ready")
            return True
        else:
            print(f"❌ Deployment {deployment_name} failed to become ready: {stderr}")
            return False
    
    def create_deployment_manifest(self, app_config: Dict[str, Any]) -> str:
        """
        Create deployment manifest from configuration.
        
        Args:
            app_config: Application configuration
            
        Returns:
            str: Path to created manifest
        """
        app_name = app_config["name"]
        manifest_path = self.manifests_dir / f"{app_name}-deployment.yaml"
        
        deployment = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": app_name,
                "namespace": self.namespace,
                "labels": {
                    "app": app_name,
                    "project": "ce-capstone-grp1"
                }
            },
            "spec": {
                "replicas": app_config.get("replicas", 1),
                "selector": {
                    "matchLabels": {
                        "app": app_name
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": app_name
                        }
                    },
                    "spec": {
                        "containers": [
                            {
                                "name": app_name,
                                "image": app_config["image"],
                                "ports": [
                                    {
                                        "containerPort": app_config["port"]
                                    }
                                ],
                                "resources": {
                                    "requests": {
                                        "memory": "64Mi",
                                        "cpu": "250m"
                                    },
                                    "limits": {
                                        "memory": "128Mi",
                                        "cpu": "500m"
                                    }
                                },
                                "livenessProbe": {
                                    "httpGet": {
                                        "path": "/",
                                        "port": app_config["port"]
                                    },
                                    "initialDelaySeconds": 30,
                                    "periodSeconds": 10
                                },
                                "readinessProbe": {
                                    "httpGet": {
                                        "path": "/",
                                        "port": app_config["port"]
                                    },
                                    "initialDelaySeconds": 5,
                                    "periodSeconds": 5
                                }
                            }
                        ]
                    }
                }
            }
        }
        
        with open(manifest_path, 'w') as f:
            yaml.dump(deployment, f, default_flow_style=False)
        
        return str(manifest_path)
    
    def create_service_manifest(self, app_config: Dict[str, Any]) -> str:
        """
        Create service manifest from configuration.
        
        Args:
            app_config: Application configuration
            
        Returns:
            str: Path to created manifest
        """
        app_name = app_config["name"]
        manifest_path = self.manifests_dir / f"{app_name}-service.yaml"
        
        service = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": f"{app_name}-service",
                "namespace": self.namespace,
                "labels": {
                    "app": app_name,
                    "project": "ce-capstone-grp1"
                }
            },
            "spec": {
                "selector": {
                    "app": app_name
                },
                "ports": [
                    {
                        "port": 80,
                        "targetPort": app_config["port"],
                        "protocol": "TCP"
                    }
                ],
                "type": app_config.get("service_type", "ClusterIP")
            }
        }
        
        with open(manifest_path, 'w') as f:
            yaml.dump(service, f, default_flow_style=False)
        
        return str(manifest_path)
    
    def deploy_application(self, app_config: Dict[str, Any]) -> bool:
        """
        Deploy a single application.
        
        Args:
            app_config: Application configuration
            
        Returns:
            bool: True if successful
        """
        app_name = app_config["name"]
        print(f"🚀 Deploying application: {app_name}")
        
        # Create manifests
        deployment_manifest = self.create_deployment_manifest(app_config)
        service_manifest = self.create_service_manifest(app_config)
        
        # Apply manifests
        if not self.apply_manifest(deployment_manifest):
            return False
        
        if not self.apply_manifest(service_manifest):
            return False
        
        # Wait for deployment to be ready
        return self.wait_for_deployment(app_name)
    
    def deploy_applications(self) -> bool:
        """
        Deploy all configured applications.
        
        Returns:
            bool: True if all deployments successful
        """
        print("📦 Deploying applications to Kubernetes...")
        
        # Check cluster connection
        if not self.check_cluster_connection():
            return False
        
        # Create namespace
        if not self.create_namespace():
            return False
        
        # Deploy each application
        apps = self.config.get("apps", [])
        
        for app_config in apps:
            if not self.deploy_application(app_config):
                print(f"❌ Failed to deploy application: {app_config['name']}")
                return False
        
        print("✅ All applications deployed successfully!")
        return True
    
    def get_application_status(self) -> Dict[str, Any]:
        """
        Get status of deployed applications.
        
        Returns:
            Dict: Application status information
        """
        status = {
            "namespace": self.namespace,
            "pods": [],
            "services": [],
            "deployments": []
        }
        
        # Get pods
        pods = self.get_pods()
        for pod in pods:
            pod_info = {
                "name": pod["metadata"]["name"],
                "status": pod["status"]["phase"],
                "ready": all(condition["status"] == "True" 
                           for condition in pod["status"].get("conditions", [])
                           if condition["type"] == "Ready")
            }
            status["pods"].append(pod_info)
        
        # Get services
        services = self.get_services()
        for service in services:
            service_info = {
                "name": service["metadata"]["name"],
                "type": service["spec"]["type"],
                "cluster_ip": service["spec"].get("clusterIP"),
                "external_ip": service["status"].get("loadBalancer", {}).get("ingress", [{}])[0].get("ip")
            }
            status["services"].append(service_info)
        
        return status
    
    def cleanup(self) -> bool:
        """
        Cleanup all deployed applications.
        
        Returns:
            bool: True if successful
        """
        print("🧹 Cleaning up Kubernetes applications...")
        
        # Delete all resources in namespace
        command = ["delete", "all", "--all", "-n", self.namespace]
        success, stdout, stderr = self.run_kubectl(command)
        
        if success:
            print("✅ Kubernetes cleanup completed")
            return True
        else:
            print(f"❌ Kubernetes cleanup failed: {stderr}")
            return False
