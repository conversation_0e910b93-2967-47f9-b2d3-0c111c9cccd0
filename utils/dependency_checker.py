"""
Dependency Checker Module
Validates and manages required tools and dependencies for the capstone project.
"""

import os
import sys
import subprocess
import platform
import logging
from typing import Dict, List, Tuple, Optional
from pathlib import Path


class DependencyChecker:
    """
    Comprehensive dependency checker for cloud engineering tools.
    Validates installation and versions of required tools.
    """
    
    def __init__(self):
        """Initialize the dependency checker."""
        self.logger = logging.getLogger(__name__)
        self.system = platform.system().lower()
        self.required_tools = {
            "python": {"min_version": "3.8", "command": "python3 --version"},
            "aws": {"min_version": "2.0", "command": "aws --version"},
            "terraform": {"min_version": "1.0", "command": "terraform version"},
            "kubectl": {"min_version": "1.20", "command": "kubectl version --client"},
            "helm": {"min_version": "3.0", "command": "helm version"},
            "docker": {"min_version": "20.0", "command": "docker --version"},
            "git": {"min_version": "2.0", "command": "git --version"}
        }
        
        self.python_packages = [
            "boto3>=1.26.0",
            "pyyaml>=6.0",
            "requests>=2.28.0",
            "click>=8.0.0",
            "rich>=12.0.0",
            "tqdm>=4.64.0"
        ]
    
    def check_command_exists(self, command: str) -> bool:
        """
        Check if a command exists in the system PATH.
        
        Args:
            command: Command to check
            
        Returns:
            bool: True if command exists, False otherwise
        """
        try:
            subprocess.run(
                ["which", command] if self.system != "windows" else ["where", command],
                check=True,
                capture_output=True,
                text=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def get_version(self, command: str) -> Optional[str]:
        """
        Get version of a command.
        
        Args:
            command: Command to get version for
            
        Returns:
            str: Version string or None if failed
        """
        try:
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return result.stderr.strip()
                
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
            return None
    
    def compare_versions(self, current: str, required: str) -> bool:
        """
        Compare version strings.
        
        Args:
            current: Current version string
            required: Required minimum version
            
        Returns:
            bool: True if current >= required
        """
        try:
            # Extract version numbers from strings
            import re
            
            current_match = re.search(r'(\d+)\.(\d+)(?:\.(\d+))?', current)
            required_match = re.search(r'(\d+)\.(\d+)(?:\.(\d+))?', required)
            
            if not current_match or not required_match:
                return False
            
            current_parts = [int(x) if x else 0 for x in current_match.groups()]
            required_parts = [int(x) if x else 0 for x in required_match.groups()]
            
            # Pad to same length
            while len(current_parts) < 3:
                current_parts.append(0)
            while len(required_parts) < 3:
                required_parts.append(0)
            
            return current_parts >= required_parts
            
        except Exception as e:
            self.logger.warning(f"Version comparison failed: {e}")
            return False
    
    def check_tool(self, tool_name: str) -> Tuple[bool, str, Optional[str]]:
        """
        Check if a specific tool is installed and meets version requirements.
        
        Args:
            tool_name: Name of the tool to check
            
        Returns:
            Tuple of (is_satisfied, status_message, version)
        """
        if tool_name not in self.required_tools:
            return False, f"Unknown tool: {tool_name}", None
        
        tool_config = self.required_tools[tool_name]
        command_parts = tool_config["command"].split()
        tool_command = command_parts[0]
        
        # Check if command exists
        if not self.check_command_exists(tool_command):
            return False, f"❌ {tool_name} not found in PATH", None
        
        # Get version
        version_output = self.get_version(tool_config["command"])
        if not version_output:
            return False, f"❌ Could not get {tool_name} version", None
        
        # Check version requirement
        min_version = tool_config["min_version"]
        if self.compare_versions(version_output, min_version):
            return True, f"✅ {tool_name} {version_output}", version_output
        else:
            return False, f"❌ {tool_name} version {version_output} < {min_version}", version_output
    
    def check_python_packages(self) -> Tuple[bool, List[str]]:
        """
        Check if required Python packages are installed.
        
        Returns:
            Tuple of (all_satisfied, missing_packages)
        """
        missing_packages = []
        
        for package in self.python_packages:
            try:
                # Parse package name and version requirement
                if ">=" in package:
                    package_name, version_req = package.split(">=")
                else:
                    package_name = package
                    version_req = None
                
                # Try to import the package
                __import__(package_name.replace("-", "_"))
                
                # TODO: Add version checking for packages
                
            except ImportError:
                missing_packages.append(package)
        
        return len(missing_packages) == 0, missing_packages
    
    def check_aws_credentials(self) -> Tuple[bool, str]:
        """
        Check if AWS credentials are configured.
        
        Returns:
            Tuple of (is_configured, status_message)
        """
        try:
            # Check for AWS credentials
            aws_config_dir = Path.home() / ".aws"
            credentials_file = aws_config_dir / "credentials"
            config_file = aws_config_dir / "config"
            
            # Check environment variables
            env_vars = ["AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"]
            env_configured = all(os.getenv(var) for var in env_vars)
            
            # Check files
            files_configured = credentials_file.exists() or config_file.exists()
            
            if env_configured or files_configured:
                # Test AWS CLI
                result = subprocess.run(
                    ["aws", "sts", "get-caller-identity"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    return True, "✅ AWS credentials configured and valid"
                else:
                    return False, "❌ AWS credentials configured but invalid"
            else:
                return False, "❌ AWS credentials not configured"
                
        except Exception as e:
            return False, f"❌ Error checking AWS credentials: {e}"
    
    def check_docker_daemon(self) -> Tuple[bool, str]:
        """
        Check if Docker daemon is running.
        
        Returns:
            Tuple of (is_running, status_message)
        """
        try:
            result = subprocess.run(
                ["docker", "info"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return True, "✅ Docker daemon is running"
            else:
                return False, "❌ Docker daemon is not running"
                
        except Exception as e:
            return False, f"❌ Error checking Docker daemon: {e}"
    
    def install_missing_python_packages(self, packages: List[str]) -> bool:
        """
        Install missing Python packages.
        
        Args:
            packages: List of packages to install
            
        Returns:
            bool: True if installation successful
        """
        if not packages:
            return True
        
        try:
            print(f"Installing missing Python packages: {', '.join(packages)}")
            
            cmd = [sys.executable, "-m", "pip", "install"] + packages
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Python packages installed successfully")
                return True
            else:
                print(f"❌ Failed to install packages: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing packages: {e}")
            return False
    
    def get_installation_instructions(self, tool_name: str) -> str:
        """
        Get installation instructions for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            str: Installation instructions
        """
        instructions = {
            "aws": {
                "macos": "brew install awscli",
                "linux": "curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip' && unzip awscliv2.zip && sudo ./aws/install",
                "windows": "Download from https://aws.amazon.com/cli/"
            },
            "terraform": {
                "macos": "brew install terraform",
                "linux": "wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip && unzip terraform_1.6.0_linux_amd64.zip && sudo mv terraform /usr/local/bin/",
                "windows": "choco install terraform"
            },
            "kubectl": {
                "macos": "brew install kubectl",
                "linux": "curl -LO 'https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl' && sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl",
                "windows": "choco install kubernetes-cli"
            },
            "helm": {
                "macos": "brew install helm",
                "linux": "curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash",
                "windows": "choco install kubernetes-helm"
            },
            "docker": {
                "macos": "brew install --cask docker",
                "linux": "curl -fsSL https://get.docker.com -o get-docker.sh && sh get-docker.sh",
                "windows": "Download Docker Desktop from https://www.docker.com/products/docker-desktop"
            }
        }
        
        if tool_name in instructions:
            system_key = "macos" if self.system == "darwin" else self.system
            return instructions[tool_name].get(system_key, "Please install manually")
        
        return "Installation instructions not available"
    
    def check_all_dependencies(self) -> bool:
        """
        Check all dependencies and provide detailed report.
        
        Returns:
            bool: True if all dependencies are satisfied
        """
        print("\n" + "=" * 60)
        print("           DEPENDENCY CHECK REPORT")
        print("=" * 60)
        
        all_satisfied = True
        
        # Check system tools
        print("\n🔧 System Tools:")
        for tool_name in self.required_tools:
            satisfied, message, version = self.check_tool(tool_name)
            print(f"  {message}")
            
            if not satisfied:
                all_satisfied = False
                print(f"     💡 Install: {self.get_installation_instructions(tool_name)}")
        
        # Check Python packages
        print("\n🐍 Python Packages:")
        packages_ok, missing_packages = self.check_python_packages()
        if packages_ok:
            print("  ✅ All required Python packages are installed")
        else:
            print(f"  ❌ Missing packages: {', '.join(missing_packages)}")
            
            # Offer to install missing packages
            install = input("  Would you like to install missing packages? (y/N): ").strip().lower()
            if install == 'y':
                if self.install_missing_python_packages(missing_packages):
                    packages_ok = True
                else:
                    all_satisfied = False
            else:
                all_satisfied = False
        
        # Check AWS credentials
        print("\n☁️  AWS Configuration:")
        aws_ok, aws_message = self.check_aws_credentials()
        print(f"  {aws_message}")
        if not aws_ok:
            print("     💡 Run: aws configure")
        
        # Check Docker daemon
        print("\n🐳 Docker:")
        docker_ok, docker_message = self.check_docker_daemon()
        print(f"  {docker_message}")
        
        # Summary
        print("\n" + "=" * 60)
        if all_satisfied and aws_ok and docker_ok:
            print("🎉 ALL DEPENDENCIES SATISFIED!")
            print("You're ready to deploy!")
        else:
            print("⚠️  SOME DEPENDENCIES NEED ATTENTION")
            print("Please resolve the issues above before proceeding.")
        print("=" * 60)
        
        return all_satisfied and aws_ok and docker_ok
