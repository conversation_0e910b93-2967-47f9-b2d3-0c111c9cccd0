"""
AWS Manager Mo<PERSON>le
Handles AWS operations and resource management for the capstone project.
"""

import boto3
import logging
from typing import Dict, List, Optional, Any, Tuple
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound


class AWSManager:
    """
    Manages AWS operations including resource creation, monitoring, and cleanup.
    Provides high-level abstractions for common AWS tasks.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize AWS manager with configuration.
        
        Args:
            config: AWS configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.region = config.get("region", "us-west-2")
        self.profile = config.get("profile", "default")
        
        # Initialize AWS session
        try:
            if self.profile != "default":
                self.session = boto3.Session(profile_name=self.profile)
            else:
                self.session = boto3.Session()
            
            # Initialize clients
            self.ec2 = self.session.client('ec2', region_name=self.region)
            self.eks = self.session.client('eks', region_name=self.region)
            self.iam = self.session.client('iam')
            self.s3 = self.session.client('s3')
            self.sts = self.session.client('sts')
            self.dynamodb = self.session.client('dynamodb', region_name=self.region)
            self.cloudformation = self.session.client('cloudformation', region_name=self.region)
            
        except (ProfileNotFound, NoCredentialsError) as e:
            self.logger.error(f"AWS configuration error: {e}")
            raise
    
    def test_connectivity(self) -> bool:
        """
        Test AWS connectivity and credentials.
        
        Returns:
            bool: True if connection successful
        """
        try:
            # Test with STS get-caller-identity
            response = self.sts.get_caller_identity()
            
            account_id = response.get('Account')
            user_arn = response.get('Arn')
            
            self.logger.info(f"AWS connection successful - Account: {account_id}, User: {user_arn}")
            print(f"✅ Connected to AWS Account: {account_id}")
            print(f"   User/Role: {user_arn}")
            print(f"   Region: {self.region}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"AWS connectivity test failed: {e}")
            print(f"❌ AWS connectivity test failed: {e}")
            return False
    
    def get_vpc_status(self) -> Dict[str, Any]:
        """
        Get VPC infrastructure status.
        
        Returns:
            Dict: VPC status information
        """
        try:
            # Get VPCs with project tags
            project_tag = self.config.get("tags", {}).get("Project", "ce-capstone-grp1")
            
            vpcs = self.ec2.describe_vpcs(
                Filters=[
                    {
                        'Name': 'tag:Project',
                        'Values': [project_tag]
                    }
                ]
            )
            
            if not vpcs['Vpcs']:
                return {
                    "status": "not_found",
                    "details": "No VPC found with project tags"
                }
            
            vpc = vpcs['Vpcs'][0]
            vpc_id = vpc['VpcId']
            
            # Get subnets
            subnets = self.ec2.describe_subnets(
                Filters=[
                    {
                        'Name': 'vpc-id',
                        'Values': [vpc_id]
                    }
                ]
            )
            
            # Get internet gateway
            igws = self.ec2.describe_internet_gateways(
                Filters=[
                    {
                        'Name': 'attachment.vpc-id',
                        'Values': [vpc_id]
                    }
                ]
            )
            
            # Get NAT gateways
            nat_gws = self.ec2.describe_nat_gateways(
                Filters=[
                    {
                        'Name': 'vpc-id',
                        'Values': [vpc_id]
                    }
                ]
            )
            
            return {
                "status": "healthy",
                "vpc_id": vpc_id,
                "cidr_block": vpc['CidrBlock'],
                "subnets": len(subnets['Subnets']),
                "internet_gateways": len(igws['InternetGateways']),
                "nat_gateways": len(nat_gws['NatGateways']),
                "details": f"VPC {vpc_id} with {len(subnets['Subnets'])} subnets"
            }
            
        except Exception as e:
            self.logger.error(f"Error getting VPC status: {e}")
            return {
                "status": "error",
                "details": str(e)
            }
    
    def get_eks_status(self) -> Dict[str, Any]:
        """
        Get EKS cluster status.
        
        Returns:
            Dict: EKS status information
        """
        try:
            # Get cluster name from config
            cluster_name = self.config.get("cluster_name", "ce-capstone-dev")
            
            # Describe cluster
            cluster = self.eks.describe_cluster(name=cluster_name)
            cluster_info = cluster['cluster']
            
            # Get node groups
            nodegroups = self.eks.list_nodegroups(clusterName=cluster_name)
            
            node_group_status = []
            for ng_name in nodegroups['nodegroups']:
                ng_info = self.eks.describe_nodegroup(
                    clusterName=cluster_name,
                    nodegroupName=ng_name
                )
                node_group_status.append({
                    "name": ng_name,
                    "status": ng_info['nodegroup']['status'],
                    "capacity": ng_info['nodegroup']['scalingConfig']
                })
            
            return {
                "status": "healthy" if cluster_info['status'] == 'ACTIVE' else cluster_info['status'].lower(),
                "cluster_name": cluster_name,
                "cluster_status": cluster_info['status'],
                "version": cluster_info['version'],
                "endpoint": cluster_info['endpoint'],
                "node_groups": node_group_status,
                "details": f"Cluster {cluster_name} ({cluster_info['status']}) with {len(node_group_status)} node groups"
            }
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                return {
                    "status": "not_found",
                    "details": f"EKS cluster not found"
                }
            else:
                return {
                    "status": "error",
                    "details": str(e)
                }
        except Exception as e:
            self.logger.error(f"Error getting EKS status: {e}")
            return {
                "status": "error",
                "details": str(e)
            }
    
    def get_s3_backend_status(self) -> Dict[str, Any]:
        """
        Get Terraform S3 backend status.
        
        Returns:
            Dict: S3 backend status information
        """
        try:
            bucket_name = self.config.get("terraform", {}).get("backend", {}).get("bucket")
            
            if not bucket_name:
                return {
                    "status": "not_configured",
                    "details": "S3 backend bucket not configured"
                }
            
            # Check if bucket exists
            try:
                self.s3.head_bucket(Bucket=bucket_name)
                
                # Get bucket location
                location = self.s3.get_bucket_location(Bucket=bucket_name)
                region = location.get('LocationConstraint') or 'us-east-1'
                
                # Check for state files
                objects = self.s3.list_objects_v2(Bucket=bucket_name, Prefix='terraform.tfstate')
                state_files = objects.get('Contents', [])
                
                return {
                    "status": "healthy",
                    "bucket_name": bucket_name,
                    "region": region,
                    "state_files": len(state_files),
                    "details": f"S3 backend {bucket_name} with {len(state_files)} state files"
                }
                
            except ClientError as e:
                if e.response['Error']['Code'] == '404':
                    return {
                        "status": "not_found",
                        "details": f"S3 bucket {bucket_name} not found"
                    }
                else:
                    return {
                        "status": "error",
                        "details": str(e)
                    }
                    
        except Exception as e:
            self.logger.error(f"Error getting S3 backend status: {e}")
            return {
                "status": "error",
                "details": str(e)
            }
    
    def get_infrastructure_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get comprehensive infrastructure status.
        
        Returns:
            Dict: Complete infrastructure status
        """
        status = {}
        
        print("🔍 Checking AWS infrastructure status...")
        
        # Check VPC
        print("   Checking VPC...")
        status["VPC"] = self.get_vpc_status()
        
        # Check EKS
        print("   Checking EKS...")
        status["EKS"] = self.get_eks_status()
        
        # Check S3 Backend
        print("   Checking Terraform Backend...")
        status["Terraform Backend"] = self.get_s3_backend_status()
        
        return status
    
    def create_s3_backend(self, bucket_name: str) -> bool:
        """
        Create S3 bucket for Terraform backend.
        
        Args:
            bucket_name: Name of the S3 bucket
            
        Returns:
            bool: True if successful
        """
        try:
            # Create bucket
            if self.region == 'us-east-1':
                self.s3.create_bucket(Bucket=bucket_name)
            else:
                self.s3.create_bucket(
                    Bucket=bucket_name,
                    CreateBucketConfiguration={'LocationConstraint': self.region}
                )
            
            # Enable versioning
            self.s3.put_bucket_versioning(
                Bucket=bucket_name,
                VersioningConfiguration={'Status': 'Enabled'}
            )
            
            # Enable encryption
            self.s3.put_bucket_encryption(
                Bucket=bucket_name,
                ServerSideEncryptionConfiguration={
                    'Rules': [
                        {
                            'ApplyServerSideEncryptionByDefault': {
                                'SSEAlgorithm': 'AES256'
                            }
                        }
                    ]
                }
            )
            
            # Block public access
            self.s3.put_public_access_block(
                Bucket=bucket_name,
                PublicAccessBlockConfiguration={
                    'BlockPublicAcls': True,
                    'IgnorePublicAcls': True,
                    'BlockPublicPolicy': True,
                    'RestrictPublicBuckets': True
                }
            )
            
            self.logger.info(f"S3 backend bucket created: {bucket_name}")
            return True

        except ClientError as e:
            if e.response['Error']['Code'] in ['BucketAlreadyExists', 'BucketAlreadyOwnedByYou']:
                self.logger.info(f"S3 bucket already exists: {bucket_name}")
                return True
            else:
                self.logger.error(f"Error creating S3 backend: {e}")
                return False
        except Exception as e:
            self.logger.error(f"Error creating S3 backend: {e}")
            return False

    def create_dynamodb_table(self, table_name: str) -> bool:
        """
        Create DynamoDB table for Terraform state locking.

        Args:
            table_name: Name of the DynamoDB table

        Returns:
            bool: True if successful
        """
        try:
            # Create table
            self.dynamodb.create_table(
                TableName=table_name,
                KeySchema=[
                    {'AttributeName': 'LockID', 'KeyType': 'HASH'}
                ],
                AttributeDefinitions=[
                    {'AttributeName': 'LockID', 'AttributeType': 'S'}
                ],
                BillingMode='PAY_PER_REQUEST',
                Tags=[
                    {'Key': 'Project', 'Value': 'ce-capstone-grp1'},
                    {'Key': 'Environment', 'Value': self.config.get('environment', 'dev')},
                    {'Key': 'Purpose', 'Value': 'terraform-state-locking'}
                ]
            )

            # Wait for table to be active
            print(f"⏳ Waiting for DynamoDB table {table_name} to be active...")
            waiter = self.dynamodb.get_waiter('table_exists')
            waiter.wait(TableName=table_name, WaiterConfig={'Delay': 5, 'MaxAttempts': 12})

            self.logger.info(f"DynamoDB table created: {table_name}")
            return True

        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceInUseException':
                self.logger.info(f"DynamoDB table already exists: {table_name}")
                return True
            else:
                self.logger.error(f"Error creating DynamoDB table: {e}")
                return False
        except Exception as e:
            self.logger.error(f"Error creating DynamoDB table: {e}")
            return False

    def create_terraform_backend_resources(self, bucket_name: str, dynamodb_table: str) -> bool:
        """
        Create both S3 bucket and DynamoDB table for Terraform backend.

        Args:
            bucket_name: Name of the S3 bucket
            dynamodb_table: Name of the DynamoDB table

        Returns:
            bool: True if successful
        """
        print(f"🔧 Setting up Terraform backend resources...")

        # Create S3 bucket
        print(f"🪣 Creating S3 bucket: {bucket_name}")
        s3_success = self.create_s3_backend(bucket_name)

        # Create DynamoDB table
        print(f"🗃️ Creating DynamoDB table: {dynamodb_table}")
        dynamodb_success = self.create_dynamodb_table(dynamodb_table)

        if s3_success and dynamodb_success:
            print("✅ Terraform backend resources created successfully")
            return True
        else:
            print("❌ Failed to create some Terraform backend resources")
            return False
    
    def cleanup_resources(self, resource_type: str = "all") -> bool:
        """
        Cleanup AWS resources based on project tags.
        
        Args:
            resource_type: Type of resources to cleanup ('vpc', 'eks', 'all')
            
        Returns:
            bool: True if successful
        """
        try:
            project_tag = self.config.get("tags", {}).get("Project", "ce-capstone-grp1")
            
            if resource_type in ["eks", "all"]:
                # Cleanup EKS clusters
                clusters = self.eks.list_clusters()
                for cluster_name in clusters['clusters']:
                    if project_tag in cluster_name:
                        print(f"🧹 Deleting EKS cluster: {cluster_name}")
                        # Note: This is a simplified cleanup
                        # In practice, you'd need to delete node groups first
                        
            if resource_type in ["vpc", "all"]:
                # Cleanup VPC resources
                vpcs = self.ec2.describe_vpcs(
                    Filters=[
                        {
                            'Name': 'tag:Project',
                            'Values': [project_tag]
                        }
                    ]
                )
                
                for vpc in vpcs['Vpcs']:
                    vpc_id = vpc['VpcId']
                    print(f"🧹 Cleaning up VPC: {vpc_id}")
                    # Note: This is a simplified cleanup
                    # In practice, you'd need to delete all resources in order
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            return False
