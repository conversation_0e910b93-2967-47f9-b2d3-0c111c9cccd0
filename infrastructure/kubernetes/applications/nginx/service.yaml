---
apiVersion: v1
kind: Service
metadata:
  name: nginx-webapp-service
  namespace: capstone-dev

  labels:
    app.kubernetes.io/name: nginx
    app.kubernetes.io/instance: nginx-webapp
    app.kubernetes.io/part-of: ce-capstone-grp1
    app.kubernetes.io/managed-by: capstone-deployer
    environment: dev

  annotations:
    # For LoadBalancer type, uncomment the following for internal LB:
    # service.beta.kubernetes.io/aws-load-balancer-internal: "true"

spec:
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
