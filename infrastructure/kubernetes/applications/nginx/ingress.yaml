---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress
  namespace: prod
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:255945442255:certificate/ab4d119c-d073-4d08-90c3-abb44ac88bc1
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80, "HTTPS": 443}]'
    alb.ingress.kubernetes.io/group.name: ce-grp-1
    alb.ingress.kubernetes.io/ssl-redirect: '443'
spec:
  rules:
  - host: ce-grp-1.sctp-sandbox.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx-prod-webapp-aalimsee
            port:
              number: 80
  tls:
  - hosts:
    - ce-grp-1.sctp-sandbox.com
    secretName: nginx-tls  # This name is symbolic; ACM manages the cert
