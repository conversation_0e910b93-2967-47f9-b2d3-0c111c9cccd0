---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-webapp
  namespace: capstone-dev

  labels:
    app.kubernetes.io/name: nginx
    app.kubernetes.io/instance: nginx-webapp
    app.kubernetes.io/part-of: ce-capstone-grp1
    app.kubernetes.io/managed-by: capstone-deployer
    environment: dev

spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
        - name: nginx
          image: nginx:latest
          ports:
            - containerPort: 80

          resources:
            requests:
              memory: "64Mi"
              cpu: "250m"
            limits:
              memory: "128Mi"
              cpu: "500m"

          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 20
