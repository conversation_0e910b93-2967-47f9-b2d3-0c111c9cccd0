# AWS Configuration
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

# Project Configuration
variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "ce-capstone-grp1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "ce-capstone-grp1"
    Environment = "dev"
    ManagedBy   = "terraform"
  }
}

# VPC Remote State Configuration
variable "vpc_state_bucket" {
  description = "S3 bucket for VPC Terraform state"
  type        = string
  default     = "ce-capstone-terraform-state-dev"
}

variable "vpc_state_key" {
  description = "S3 key for VPC Terraform state"
  type        = string
  default     = "vpc/terraform.tfstate"
}

# EKS Configuration
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = "ce-capstone-eks"
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "cluster_endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
  default     = true
}

variable "cluster_endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
  default     = true
}

variable "enable_cluster_creator_admin_permissions" {
  description = "Enable cluster creator admin permissions"
  type        = bool
  default     = true
}

# Node Groups Configuration
variable "node_groups" {
  description = "EKS managed node groups configuration"
  type = map(object({
    instance_types = list(string)
    desired_size   = number
    min_size       = number
    max_size       = number
    disk_size      = optional(number, 20)
    ami_type       = optional(string, "AL2_x86_64")
  }))
  default = {
    main = {
      instance_types = ["t3.medium"]
      desired_size   = 2
      min_size       = 1
      max_size       = 3
      disk_size      = 20
      ami_type       = "AL2_x86_64"
    }
  }
}

# Access Entries Configuration
variable "access_entries" {
  description = "EKS access entries for users and roles"
  type = map(object({
    principal_arn       = string
    kubernetes_groups   = list(string)
    policy_associations = list(string)
  }))
  default = {}
}
