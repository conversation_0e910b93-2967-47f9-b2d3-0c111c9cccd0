# EditorConfig is awesome: http://EditorConfig.org
# Uses editorconfig to maintain consistent coding styles

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
indent_size = 2
indent_style = space
insert_final_newline = true
max_line_length = 80
trim_trailing_whitespace = true

[*.{tf,tfvars}]
indent_size = 2
indent_style = space

[*.md]
max_line_length = 0
trim_trailing_whitespace = false

[Makefile]
tab_width = 2
indent_style = tab

[COMMIT_EDITMSG]
max_line_length = 0
