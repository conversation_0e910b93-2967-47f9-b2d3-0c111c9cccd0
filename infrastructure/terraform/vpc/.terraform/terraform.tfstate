{"version": 3, "terraform_version": "1.12.1", "backend": {"type": "s3", "config": {"access_key": null, "acl": null, "allowed_account_ids": null, "assume_role": null, "assume_role_with_web_identity": null, "bucket": "ce-capstone-terraform-state-dev", "custom_ca_bundle": null, "dynamodb_endpoint": null, "dynamodb_table": "ce-capstone-terraform-locks-dev", "ec2_metadata_service_endpoint": null, "ec2_metadata_service_endpoint_mode": null, "encrypt": true, "endpoint": null, "endpoints": null, "forbidden_account_ids": null, "force_path_style": null, "http_proxy": null, "https_proxy": null, "iam_endpoint": null, "insecure": null, "key": "vpc/terraform.tfstate", "kms_key_id": null, "max_retries": null, "no_proxy": null, "profile": null, "region": "us-east-1", "retry_mode": null, "secret_key": null, "shared_config_files": null, "shared_credentials_file": null, "shared_credentials_files": null, "skip_credentials_validation": null, "skip_metadata_api_check": null, "skip_region_validation": null, "skip_requesting_account_id": null, "skip_s3_checksum": null, "sse_customer_key": null, "sts_endpoint": null, "sts_region": null, "token": null, "use_dualstack_endpoint": null, "use_fips_endpoint": null, "use_lockfile": null, "use_path_style": null, "workspace_key_prefix": null}, "hash": **********}}