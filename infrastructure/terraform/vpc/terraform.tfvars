# Auto-generated Terraform variables
# Generated by Capstone Deployer

aws_region = "us-east-1"
project_name = "ce-capstone-grp1"
environment = "dev"
vpc_name = "ce-capstone-dev-vpc"
vpc_cidr = "10.0.0.0/16"
availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]
private_subnets = ["*********/24", "*********/24", "*********/24"]
public_subnets = ["********/24", "********/24", "********/24"]
cluster_name = "ce-capstone-dev"
enable_nat_gateway = true
single_nat_gateway = true
enable_dns_hostnames = true
enable_dns_support = true
common_tags = {
  Project = "ce-capstone-grp1"
  Environment = "dev"
  ManagedBy = "capstone-deployer"
}
