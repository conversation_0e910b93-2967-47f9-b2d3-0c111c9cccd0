# Backend configuration will be provided via backend config file or CLI
# This allows for dynamic backend configuration per environment
terraform {
  backend "s3" {
    # Configuration will be provided via:
    # terraform init -backend-config=backend.hcl
    # or environment variables
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  required_version = ">= 1.0"
}
