provider "aws" {
  region = var.aws_region
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.1.1"

  name = var.vpc_name
  cidr = var.vpc_cidr

  azs             = var.availability_zones
  private_subnets = var.private_subnets
  public_subnets  = var.public_subnets

  enable_nat_gateway   = var.enable_nat_gateway
  single_nat_gateway   = var.single_nat_gateway
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support

  # Subnet tags
  public_subnet_tags = {
    Name                                        = "${var.project_name}-public"
    "kubernetes.io/role/elb"                    = "1"
    "kubernetes.io/cluster/${var.cluster_name}" = "shared" # Add Required Kubernetes Tags to Subnets
  }

  private_subnet_tags = {
    Name                                        = "${var.project_name}-private"
    "kubernetes.io/role/internal-elb"           = "1"
    "kubernetes.io/cluster/${var.cluster_name}" = "shared" # Add Required Kubernetes Tags to Subnets
  }


  # VPC tag
  tags = merge(var.common_tags, {
    Name = var.vpc_name
  })

  # Route table tags
  default_route_table_tags = merge(var.common_tags, {
    Name = "${var.project_name}-default-rt"
  })
  public_route_table_tags = merge(var.common_tags, {
    Name = "${var.project_name}-public-rt"
  })
  private_route_table_tags = merge(var.common_tags, {
    Name = "${var.project_name}-private-rt"
  })

  default_network_acl_tags = merge(var.common_tags, {
    Name = "${var.project_name}-default-acl"
  })

  default_security_group_tags = merge(var.common_tags, {
    Name = "${var.project_name}-default-sg"
  })

  # NAT Gateway tag
  nat_gateway_tags = merge(var.common_tags, {
    Name = "${var.project_name}-nat-gw"
  })
  # Internet Gateway tag
  igw_tags = merge(var.common_tags, {
    Name = "${var.project_name}-igw"
  })
}
