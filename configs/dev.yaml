environment: dev
aws:
  region: us-west-2
  profile: default
  tags:
    Project: ce-capstone-grp1
    Environment: dev
    ManagedBy: capstone-deployer

vpc:
  cidr_block: 10.0.0.0/16
  availability_zones:
    - us-west-2a
    - us-west-2b
    - us-west-2c
  public_subnets:
    - ********/24
    - ********/24
    - ********/24
  private_subnets:
    - *********/24
    - *********/24
    - *********/24
  enable_nat_gateway: true
  enable_vpn_gateway: false
  enable_dns_hostnames: true
  enable_dns_support: true

eks:
  cluster_name: ce-capstone-dev
  cluster_version: "1.28"
  node_groups:
    main:
      instance_types:
        - t3.medium
      min_size: 1
      max_size: 3
      desired_size: 2
      disk_size: 20
      ami_type: AL2_x86_64
  addons:
    - vpc-cni
    - coredns
    - kube-proxy
    - aws-ebs-csi-driver

applications:
  namespace: capstone-dev
  apps:
    - name: frontend
      image: nginx:latest
      replicas: 2
      port: 80
      service_type: LoadBalancer
    - name: backend
      image: node:18-alpine
      replicas: 2
      port: 3000
      service_type: ClusterIP

terraform:
  backend:
    type: s3
    bucket: ce-capstone-terraform-state-dev
    key: terraform.tfstate
    region: us-west-2
    encrypt: true
    dynamodb_table: ce-capstone-terraform-locks-dev
  workspace: dev

monitoring:
  enable_cloudwatch: true
  enable_prometheus: true
  log_retention_days: 7

security:
  enable_pod_security_policy: true
  enable_network_policy: true
  allowed_cidr_blocks:
    - 0.0.0.0/0
