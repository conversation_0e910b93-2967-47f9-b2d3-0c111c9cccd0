environment: prod
aws:
  region: us-east-1
  profile: default
  tags:
    Project: ce-capstone-grp1
    Environment: prod
    ManagedBy: capstone-deployer

vpc:
  cidr_block: ********/16
  availability_zones:
    - us-east-1a
    - us-east-1b
    - us-east-1c
  public_subnets:
    - ********/24
    - ********/24
    - ********/24
  private_subnets:
    - *********/24
    - *********/24
    - *********/24
  enable_nat_gateway: true
  enable_vpn_gateway: false
  enable_dns_hostnames: true
  enable_dns_support: true

eks:
  cluster_name: ce-capstone-prod
  cluster_version: "1.28"
  node_groups:
    main:
      instance_types:
        - t3.large
      min_size: 3
      max_size: 10
      desired_size: 5
      disk_size: 50
      ami_type: AL2_x86_64
  addons:
    - vpc-cni
    - coredns
    - kube-proxy
    - aws-ebs-csi-driver

applications:
  namespace: capstone-prod
  apps:
    - name: frontend
      image: nginx:latest
      replicas: 5
      port: 80
      service_type: LoadBalancer
    - name: backend
      image: node:18-alpine
      replicas: 5
      port: 3000
      service_type: ClusterIP

terraform:
  backend:
    type: s3
    bucket: ce-capstone-terraform-state-prod
    key: terraform.tfstate
    region: us-east-1
    encrypt: true
    dynamodb_table: ce-capstone-terraform-locks-prod
  workspace: prod

monitoring:
  enable_cloudwatch: true
  enable_prometheus: true
  log_retention_days: 30

security:
  enable_pod_security_policy: true
  enable_network_policy: true
  allowed_cidr_blocks:
    - 10.0.0.0/8
