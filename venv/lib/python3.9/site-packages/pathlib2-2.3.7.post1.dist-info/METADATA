Metadata-Version: 2.1
Name: pathlib2
Version: 2.3.7.post1
Summary: Object-oriented filesystem paths
Home-page: https://github.com/jazzband/pathlib2
Author: <PERSON>
Author-email: matthias.troff<PERSON><PERSON>@gmail.com
License: MIT
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: System :: Filesystems
Requires-Dist: six
Requires-Dist: scandir ; python_version<"3.5"
Requires-Dist: typing ; python_version<"3.5"

The `old pathlib <https://web.archive.org/web/20181106215056/https://bitbucket.org/pitrou/pathlib/>`_
module on bitbucket is no longer maintained.
The goal of pathlib2 is to provide a backport of
`standard pathlib <http://docs.python.org/dev/library/pathlib.html>`_
module which tracks the standard library module,
so all the newest features of the standard pathlib can be
used also on older Python versions.

Download
--------

Standalone releases are available on PyPI:
http://pypi.python.org/pypi/pathlib2/

Development
-----------

The main development takes place in the Python standard library: see
the `Python developer's guide <http://docs.python.org/devguide/>`_.
In particular, new features should be submitted to the
`Python bug tracker <http://bugs.python.org/>`_.

Issues that occur in this backport, but that do not occur not in the
standard Python pathlib module can be submitted on
the `pathlib2 bug tracker <https://github.com/jazzband/pathlib2/issues>`_.

Documentation
-------------

Refer to the
`standard pathlib <http://docs.python.org/dev/library/pathlib.html>`_
documentation.

Known Issues
------------

For historic reasons, pathlib2 still uses bytes to represent file paths internally.
Unfortunately, on Windows with Python 2.7, the file system encoder (``mcbs``)
has only poor support for non-ascii characters,
and can silently replace non-ascii characters without warning.
For example, ``u'тест'.encode(sys.getfilesystemencoding())`` results in ``????``
which is obviously completely useless.

Therefore, on Windows with Python 2.7, until this problem is fixed upstream,
unfortunately you cannot rely on pathlib2 to support the full unicode range for filenames.
See `issue #56 <https://github.com/jazzband/pathlib2/issues/56>`_ for more details.

.. |github| image:: https://github.com/jazzband/pathlib2/actions/workflows/python-package.yml/badge.svg
   :target: https://github.com/jazzband/pathlib2/actions/workflows/python-package.yml
   :alt: github

.. |codecov| image:: https://codecov.io/gh/jazzband/pathlib2/branch/develop/graph/badge.svg
    :target: https://codecov.io/gh/jazzband/pathlib2
    :alt: codecov

.. |jazzband| image:: https://jazzband.co/static/img/badge.svg
   :alt: Jazzband
   :target: https://jazzband.co/


