# Cloud Engineering Capstone Project - Group 1

## 🎯 Project Overview

This repository consolidates AWS infrastructure deployment for the Cloud Engineering Cohort 9 - Group 1 capstone project. It provides a unified, production-ready deployment system that automates the creation and management of:

- **VPC Infrastructure**: Complete network setup with public/private subnets
- **EKS Cluster**: Managed Kubernetes cluster with node groups
- **Applications**: Containerized applications deployed to EKS

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    AWS Cloud Infrastructure                 │
├─────────────────────────────────────────────────────────────┤
│  VPC (10.x.0.0/16)                                        │
│  ├── Public Subnets (3 AZs)                               │
│  │   ├── Internet Gateway                                 │
│  │   └── Load Balancers                                   │
│  ├── Private Subnets (3 AZs)                              │
│  │   ├── EKS Cluster                                      │
│  │   ├── Worker Nodes                                     │
│  │   └── Applications                                     │
│  └── NAT Gateways                                         │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

1. **AWS CLI** (v2.0+)
2. **Terraform** (v1.0+)
3. **kubectl** (v1.20+)
4. **Helm** (v3.0+)
5. **Docker** (v20.0+)
6. **Python** (v3.8+)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd ce-grp-1-automation
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure AWS credentials:**
   ```bash
   aws configure
   ```

4. **Run the deployment script:**
   ```bash
   python deploy.py
   ```

## 📋 Menu Options

The main deployment script provides an interactive menu with the following options:

1. **🔍 Check Dependencies & Environment** - Validates all required tools
2. **⚙️ Configure AWS Credentials & Settings** - Sets up AWS connectivity
3. **🏗️ Deploy VPC Infrastructure** - Creates network infrastructure
4. **🚀 Deploy EKS Cluster** - Sets up Kubernetes cluster
5. **📦 Deploy Applications** - Deploys containerized apps
6. **🔄 Full Stack Deployment** - Complete end-to-end deployment
7. **📊 Check Infrastructure Status** - Monitors resource health
8. **🧹 Cleanup Resources** - Removes all deployed resources
9. **📋 View Logs** - Displays deployment logs
10. **⚙️ Configuration Management** - Manages environment settings

## 🔧 Configuration

### Environment Files

The system supports multiple environments through YAML configuration files:

- `configs/dev.yaml` - Development environment
- `configs/staging.yaml` - Staging environment  
- `configs/prod.yaml` - Production environment

### Key Configuration Sections

#### AWS Settings
```yaml
aws:
  region: us-west-2
  profile: default
  tags:
    Project: ce-capstone-grp1
    Environment: dev
```

#### VPC Configuration
```yaml
vpc:
  cidr_block: 10.0.0.0/16
  availability_zones:
    - us-west-2a
    - us-west-2b
    - us-west-2c
  public_subnets:
    - ********/24
    - ********/24
    - ********/24
  private_subnets:
    - *********/24
    - *********/24
    - *********/24
```

#### EKS Configuration
```yaml
eks:
  cluster_name: ce-capstone-dev
  cluster_version: "1.28"
  node_groups:
    main:
      instance_types:
        - t3.medium
      min_size: 1
      max_size: 3
      desired_size: 2
```

## 🛠️ Usage Examples

### Deploy to Development Environment
```bash
python deploy.py --environment dev
```

### Deploy to Production with Custom Config
```bash
python deploy.py --environment prod --config configs/custom-prod.yaml
```

### Enable Verbose Logging
```bash
python deploy.py --verbose
```

### Full Stack Deployment
```bash
python deploy.py
# Select option 6 from the menu
```

## 📁 Project Structure

```
ce-grp-1-automation/
├── deploy.py                 # Main orchestrator script
├── requirements.txt          # Python dependencies
├── README.md                # This file
├── utils/                   # Utility modules
│   ├── __init__.py
│   ├── aws_manager.py       # AWS operations
│   ├── config_handler.py    # Configuration management
│   ├── dependency_checker.py # Tool validation
│   ├── k8s_manager.py       # Kubernetes operations
│   ├── progress_tracker.py  # Progress monitoring
│   └── terraform_manager.py # Terraform operations
├── configs/                 # Environment configurations
│   ├── dev.yaml
│   ├── staging.yaml
│   └── prod.yaml
├── infrastructure/          # Infrastructure code
│   ├── terraform/           # Terraform modules
│   │   ├── vpc/
│   │   ├── eks/
│   │   └── modules/
│   └── kubernetes/          # Kubernetes manifests
│       ├── applications/
│       └── monitoring/
└── logs/                    # Application logs
    ├── capstone_dev.log
    ├── capstone_staging.log
    └── capstone_prod.log
```

## 🔍 Monitoring and Logging

### Log Files
- Application logs are stored in the `logs/` directory
- Each environment has its own log file
- Logs include timestamps, log levels, and detailed messages

### Progress Tracking
- Real-time progress bars for long-running operations
- Task status monitoring with success/failure indicators
- Duration tracking for performance analysis

### Infrastructure Status
- Automated health checks for all deployed resources
- Real-time status reporting for VPC, EKS, and applications
- Integration with AWS APIs for accurate status information

## 🔒 Security Features

- **IAM Integration**: Uses AWS IAM roles and policies
- **Network Security**: Private subnets for sensitive resources
- **Encryption**: S3 backend encryption for Terraform state
- **Access Control**: Kubernetes RBAC and network policies
- **Secret Management**: Secure handling of credentials

## 🧪 Testing

### Dependency Validation
The system automatically validates all required dependencies:
- Checks tool versions against minimum requirements
- Validates AWS connectivity and permissions
- Verifies Terraform and kubectl configurations

### Infrastructure Testing
- Automated health checks after deployment
- Connectivity tests for all components
- Application readiness probes

## 🚨 Troubleshooting

### Common Issues

1. **AWS Credentials Not Found**
   ```bash
   aws configure
   # or set environment variables
   export AWS_ACCESS_KEY_ID=your_key
   export AWS_SECRET_ACCESS_KEY=your_secret
   ```

2. **Terraform Backend Issues**
   - Ensure S3 bucket exists and is accessible
   - Check DynamoDB table for state locking
   - Verify IAM permissions for backend operations

3. **EKS Connection Issues**
   ```bash
   aws eks update-kubeconfig --region us-west-2 --name ce-capstone-dev
   ```

4. **Application Deployment Failures**
   - Check pod logs: `kubectl logs -n capstone-dev <pod-name>`
   - Verify resource limits and requests
   - Check service connectivity

### Getting Help

1. Check the logs in the `logs/` directory
2. Use the verbose mode: `python deploy.py --verbose`
3. Review the infrastructure status from the menu
4. Check AWS CloudWatch for detailed metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is part of the Cloud Engineering Capstone program and is intended for educational purposes.

## 👥 Team

**Cloud Engineering Cohort 9 - Group 1**

---

For questions or support, please refer to the troubleshooting section or check the application logs.
